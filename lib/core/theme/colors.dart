import 'package:flutter/material.dart';

/// Comprehensive app color palette based on the Safea design system
/// This class consolidates all color definitions for the app
class AppColors {
  // Private constructor to prevent instantiation
  AppColors._();

  // ============================================================================
  // PRIMARY BRAND COLORS
  // ============================================================================

  static const Color primary = Color(0xFF00FF88); // Bright green from designs
  static const Color primaryColor = Color(
    0xFF00FF88,
  ); // Alias for compatibility
  static const Color primaryDark = Color(0xFF00CC6A);
  static const Color primaryLight = Color(0xFF33FF99);
  static const Color onPrimary = Color(0xFFFFFFFF);

  static const Color ellipseGreen = Color(0xFF10C053);
  static const Color ellipseRed = Color(0xFFA12139);

  // ============================================================================
  // BACKGROUND COLORS
  // ============================================================================

  static const Color background = Color(0xFF070D0A); // Main background
  static const Color backgroundBlack = Color(0xFF000000);
  static const Color backgroundSecondary = Color(0xFF111111);
  static const Color backgroundTertiary = Color(0xFF1A1A1A);

  // Surface colors
  static const Color surface = Color(0xFF1E1E1E);
  static const Color surfaceVariant = Color(0xFF2A2A2A);
  static const Color surfaceContainer = Color(0xFF333333);

  // ============================================================================
  // TEXT COLORS (Consolidated from AColor)
  // ============================================================================

  // Primary text colors
  static const Color textPrimary = Color(0xFF000000); // Black
  static const Color textSecondary = Color(0xFF666666); // Medium gray
  static const Color textTertiary = Color(0xFF999999); // Light gray

  // Background-based text colors
  static const Color textOnLight = Color(0xFF000000); // Black
  static const Color textOnDark = Color(
    0xFFD7D7D7,
  ); // Light gray for dark backgrounds
  static const Color textOnPrimary = Color(0xFF000000); // Black
  static const Color textOnSecondary = Color(0xFFFFFFFF); // White

  // Semantic text colors
  static const Color textSuccess = Color(0xFF00FF88); // Safea green
  static const Color textError = Color(0xFFFF4444); // Red
  static const Color textInfo = Color(0xFF0088FF); // Blue
  static const Color textWarning = Color(0xFFFF8585); // Black

  // Interactive text colors
  static const Color textLink = Color(0xFF00FF88); // Safea green
  static const Color textLinkPressed = Color(0xFF00CC6A); // Darker green
  static const Color textDisabled = Color(0xFFCCCCCC); // Light gray

  // Specialized text colors
  static const Color textBase = Color(0xFF0D0D0D); // Very dark gray
  static const Color textMuted = Color(0xFF808080); // Medium gray
  static const Color textSubtle = Color(0xFFB3B3B3); // Light gray
  static const Color textInverse = Color(0xFFFFFFFF); // White

  // Brand-specific text colors
  static const Color textBrand = Color(0xFF00FF88); // Safea green
  static const Color textBrandVariant = Color(0xFF00CC66); // Medium green
  static const Color textBrandDark = Color(0xFF009944); // Dark green

  // Legacy text color aliases (for compatibility)
  static const Color onBackground = Color(0xFFFFFFFF); // White text
  static const Color onBackgroundSecondary = Color(0xFFB3B3B3); // Light gray
  static const Color onBackgroundTertiary = Color(0xFF808080); // Medium gray
  static const Color onSurface = Color(0xFFFFFFFF);
  static const Color onSurfaceVariant = Color(0xFFCCCCCC);

  // AppLayoutTemplate specific colors
  static const Color titleText = Color(0xFFD7D7D7); // Title text
  static const Color descriptionText = Color(0xFFFFFFFF); // Description text

  // Accent colors
  static const Color accent = Color(0xFF00FF88);
  static const Color accentSecondary = Color(0xFF88FF00);

  // Status colors
  static const Color success = Color(0xFF01EB6A); // Timer/success color
  static const Color warning = Color(0xFFFFAA00);
  static const Color error = Color(0xFFFF4444);
  static const Color info = Color(0xFF0088FF);

  // Required field indicator color
  static const Color requiredFieldIndicator = Color(
    0xFF05D761,
  ); // Green for asterisks

  // Interactive colors
  static const Color interactive = Color(0xFF00FF88);
  static const Color interactiveHover = Color(0xFF33FF99);
  static const Color interactivePressed = Color(0xFF00CC6A);
  static const Color interactiveDisabled = Color(0xFF404040);

  // Border colors
  static const Color border = Color(0xFF333333);
  static const Color outline = Color(0xFF333333); // Alias for compatibility
  static const Color borderFocus = Color(0xFF00FF88);
  static const Color borderError = Color(0xFFFF4444);

  // Form field colors - Updated with specified hex values
  static const Color inputBackground = Color(0xFF1A1A1A);
  static const Color inputBorder = Color(0xFF555555);
  static const Color inputBorderFocus = Color(0xFF00FF88);
  static const Color inputText = Color(0xFFFFFFFF); // Form field input text
  static const Color inputHint = Color(0xFF828282); // Form field label color

  // Button colors - Updated with specified hex values
  static const Color buttonPrimary = Color(0xFFFFFFFF);
  static const Color buttonPrimaryText = Color(
    0xFF1F1F1F,
  ); // Primary button text
  static const Color buttonDashboard = Color(0xFFC8C8C8);
  static const Color buttonSecondary = Color(0xFF333333);
  static const Color buttonSecondaryText = Color(0xFFFFFFFF);
  static const Color buttonOutline = Color(0xFF00FF88);
  static const Color buttonOutlineText = Color(0xFF00FF88);
  static const Color buttonRedText = Color(0xFFFF0A0A);
  static const Color buttonRedBackground = Color(0xFFFF9B9B);

  // Navigation/Selection colors - Updated with specified hex values
  static const Color activeTab = Color(0xFFD7D7D7); // Active tab/selection
  static const Color passiveTab = Color(0xFF3F3F3F); // Passive/inactive tab

  // Gradient colors for the signature green glow effect
  static const List<Color> primaryGradient = [
    Color(0xFF00FF88),
    Color(0xFF00CC6A),
    Color(0xFF009944),
  ];

  static const List<Color> backgroundGradient = [
    Color(0xFF000000),
    Color(0xFF001100),
    Color(0xFF000000),
  ];

  // Glow effect colors
  static const Color glowPrimary = Color(0x4400FF88); // 26% opacity
  static const Color glowSecondary = Color(0x2200FF88); // 13% opacity
  static const Color glowTertiary = Color(0x1100FF88); // 6% opacity

  // Overlay colors
  static const Color overlay = Color(0x80000000); // 50% black
  static const Color overlayLight = Color(0x40000000); // 25% black
  static const Color overlayHeavy = Color(0xCC000000); // 80% black

  // Timer and progress colors - Updated with specified hex values
  static const Color timerActive = Color(0xFF01EB6A); // Timer text color
  static const Color timerInactive = Color(0xFF333333);
  static const Color progressBackground = Color(0xFF1A1A1A);
  static const Color progressForeground = Color(0xFF01EB6A);

  // Card and container colors
  static const Color cardBackground = Color(0xFF1A1A1A);
  static const Color cardBorder = Color(0xFF333333);
  static const Color cardShadow = Color(0x40000000);

  // Navigation colors
  static const Color navigationBackground = Color(0xFF111111);
  static const Color navigationSelected = Color(0xFF00FF88);
  static const Color navigationUnselected = Color(0xFF666666);

  // Verification code colors
  static const Color codeInputBackground = Color(0xFF1A1A1A);
  static const Color codeInputBorder = Color(0xFF333333);
  static const Color codeInputActive = Color(0xFF00FF88);
  static const Color codeInputFilled = Color(0xFF00FF88);

  // Additional compatibility aliases for existing code
  static const Color secondaryColor = Color(0xFF10B981);
  static const Color onSecondary = Color(0xFFFFFFFF);
  static const Color onError = Color(0xFFFFFFFF);
  static const Color shadow = Color(0xFF000000);
  static const Color scrim = Color(0xFF000000);

  // ============================================================================
  // HELPER METHODS (Consolidated from AColor)
  // ============================================================================

  /// Get text color based on background brightness
  static Color getTextColorForBackground(Color backgroundColor) {
    // Calculate luminance to determine if background is light or dark
    final luminance = backgroundColor.computeLuminance();
    return luminance > 0.5 ? textOnLight : textOnDark;
  }

  /// Get contrasting text color for better readability
  static Color getContrastingTextColor(Color backgroundColor) {
    final luminance = backgroundColor.computeLuminance();
    if (luminance > 0.7) {
      return textPrimary; // Use dark text on very light backgrounds
    } else if (luminance > 0.3) {
      return textSecondary; // Use medium text on medium backgrounds
    } else {
      return textOnDark; // Use light text on dark backgrounds
    }
  }

  /// Get text color with opacity
  static Color withOpacity(Color color, double opacity) {
    return color.withValues(alpha: opacity);
  }
}
