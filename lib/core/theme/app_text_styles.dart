import 'package:flutter/material.dart';
import 'package:safea/core/theme/colors.dart';
import 'package:safea/core/theme/responsive_extensions.dart';

/// Comprehensive text styles for the Safea app with responsive support
class AppTextStyles {
  AppTextStyles._();

  // Font family
  static const String fontFamily = 'Sana Sans VAR'; // Primary app font

  // Font weights
  static const FontWeight light = FontWeight.w300;
  static const FontWeight regular = FontWeight.w400;
  static const FontWeight medium = FontWeight.w500;
  static const FontWeight semiBold = FontWeight.w600;

  // ===== ESSENTIAL TEXT STYLES =====

  static const TextStyle appLogoText = TextStyle(
    fontFamily: 'Sana Sans VAR',
    fontSize: 17,
    fontWeight: FontWeight.w400,
    color: AppColors.textOnDark,
    letterSpacing: -1,
  );

  static const TextStyle small = TextStyle(
    fontFamily: fontFamily,
    fontSize: 9,
    fontWeight: light,
    color: AppColors.onBackground,
    letterSpacing: 1.45,
  );

  static const TextStyle base = TextStyle(
    fontFamily: fontFamily,
    fontSize: 12.29,
    fontWeight: regular,
    color: AppColors.textBase,
  );
  static const TextStyle stateText = TextStyle(
    fontFamily: 'Sana Sans VAR',
    fontSize: 12,
    height: 19.6 / 12,
    fontWeight: regular,
    color: AppColors.textOnDark,
    letterSpacing: 0,
  );

  /// Button text under spanned text - weight 300, style light, size 12, color: #000000
  static const TextStyle buttonUnder = TextStyle(
    fontFamily: fontFamily,
    fontSize: 12,
    fontWeight: light,
    color: AppColors.textPrimary,
  );

  /// Tagline style - for main hero text on start screen
  static const TextStyle tagline = TextStyle(
    fontFamily: fontFamily,
    fontSize: 32,
    fontWeight: semiBold,
    height: 1.2,
    color: AppColors.textOnDark,
  );

  // Display styles (largest)
  static const TextStyle displayLarge = TextStyle(
    fontFamily: fontFamily,
    fontSize: 57,
    fontWeight: regular,
    height: 1.12,
    letterSpacing: -0.25,
  );

  static const TextStyle displayMedium = TextStyle(
    fontFamily: fontFamily,
    fontSize: 45,
    fontWeight: regular,
    height: 1.16,
  );

  static const TextStyle displaySmall = TextStyle(
    fontFamily: fontFamily,
    fontSize: 36,
    fontWeight: regular,
    height: 1.22,
  );

  // Headline styles
  static const TextStyle headlineLarge = TextStyle(
    fontFamily: fontFamily,
    fontSize: 32,
    fontWeight: regular,
    height: 1.25,
  );

  static const TextStyle headlineMedium = TextStyle(
    fontFamily: fontFamily,
    fontSize: 28,
    fontWeight: regular,
    height: 1.29,
  );

  static const TextStyle headlineSmall = TextStyle(
    fontFamily: fontFamily,
    fontSize: 24,
    fontWeight: regular,
    height: 1.33,
  );

  // Title styles
  static const TextStyle titleLarge = TextStyle(
    fontFamily: fontFamily,
    fontSize: 22,
    fontWeight: regular,
    height: 1.27,
  );

  static const TextStyle authTabs = TextStyle(
    fontFamily: fontFamily,
    fontSize: 30,
    fontWeight: regular,
    height: 36 / 30,
    letterSpacing: -1,
  );

  static const TextStyle setupTabs = TextStyle(
    fontFamily: fontFamily,
    fontSize: 40,
    fontWeight: regular,
    height: 38 / 40,
    letterSpacing: -1,
  );

  static const TextStyle dashboardTabs = TextStyle(
    fontFamily: fontFamily,
    fontSize: 40,
    fontWeight: regular,
    height: 39 / 40,
    letterSpacing: -1.5,
  );

  static const TextStyle hours = TextStyle(
    fontFamily: fontFamily,
    fontSize: 13,
    fontWeight: regular,
    height: 19.6 / 13,
    letterSpacing: 0,
  );

  static const TextStyle titleMedium = TextStyle(
    fontFamily: fontFamily,
    fontSize: 16,
    fontWeight: medium,
    height: 1.50,
    letterSpacing: 0.15,
  );

  static const TextStyle titleSmall = TextStyle(
    fontFamily: fontFamily,
    fontSize: 14,
    fontWeight: medium,
    height: 1.43,
    letterSpacing: 0.1,
  );

  // Label styles
  static const TextStyle labelLarge = TextStyle(
    fontFamily: fontFamily,
    fontSize: 14,
    fontWeight: medium,
    height: 1.43,
    letterSpacing: 0.1,
  );

  static const TextStyle labelMedium = TextStyle(
    fontFamily: fontFamily,
    fontSize: 12,
    fontWeight: medium,
    height: 1.33,
    letterSpacing: 0.5,
  );

  static const TextStyle labelSmall = TextStyle(
    fontFamily: fontFamily,
    fontSize: 11,
    fontWeight: medium,
    height: 1.45,
    letterSpacing: 0.5,
  );

  // Body styles
  static const TextStyle bodyLarge = TextStyle(
    fontFamily: fontFamily,
    fontSize: 16,
    fontWeight: regular,
    height: 1.50,
    letterSpacing: 0.15,
  );

  static const TextStyle bodyMedium = TextStyle(
    fontFamily: fontFamily,
    fontSize: 14,
    fontWeight: regular,
    height: 1.43,
    letterSpacing: 0,
  );

  static const TextStyle bodySmall = TextStyle(
    fontFamily: fontFamily,
    fontSize: 12,
    fontWeight: regular,
    height: 1.33,
    letterSpacing: 0.4,
  );

  // Custom styles for specific use cases
  static const TextStyle buttonLarge = TextStyle(
    fontFamily: fontFamily,
    fontSize: 16,
    fontWeight: semiBold,
    height: 1.25,
    letterSpacing: 0.1,
  );

  static const TextStyle buttonMedium = TextStyle(
    fontFamily: fontFamily,
    fontSize: 14,
    fontWeight: semiBold,
    height: 1.29,
    letterSpacing: 0.1,
  );

  static const TextStyle buttonSmall = TextStyle(
    fontFamily: fontFamily,
    fontSize: 12,
    fontWeight: semiBold,
    height: 1.33,
    letterSpacing: 0.1,
  );

  static const TextStyle caption = TextStyle(
    fontFamily: fontFamily,
    fontSize: 12,
    fontWeight: regular,
    height: 1.33,
    letterSpacing: 0.4,
  );

  static const TextStyle overline = TextStyle(
    fontFamily: fontFamily,
    fontSize: 10,
    fontWeight: medium,
    height: 1.6,
    letterSpacing: 1.5,
  );

  // ============================================================================
  // BUTTON TEXT STYLES (Consolidated from text_styles.dart)
  // ============================================================================

  /// Primary button text style
  static const TextStyle buttonPrimary = TextStyle(
    fontFamily: fontFamily,
    fontSize: 16,
    fontWeight: semiBold,
    color: AppColors.buttonPrimaryText,
  );

  /// Secondary button text style
  static const TextStyle buttonSecondary = TextStyle(
    fontFamily: fontFamily,
    fontSize: 16,
    fontWeight: medium,
    color: AppColors.buttonSecondaryText,
  );

  /// Outline button text style
  static const TextStyle buttonOutline = TextStyle(
    fontFamily: fontFamily,
    fontSize: 16,
    fontWeight: medium,
    color: AppColors.buttonOutlineText,
  );

  // ============================================================================
  // INPUT FIELD STYLES (Consolidated from text_styles.dart)
  // ============================================================================

  /// Input field label style
  static const TextStyle inputLabel = TextStyle(
    fontFamily: fontFamily,
    fontSize: 14,
    fontWeight: medium,
    color: AppColors.onBackgroundSecondary,
  );

  /// Input field text style
  static const TextStyle inputText = TextStyle(
    fontFamily: fontFamily,
    fontSize: 16,
    fontWeight: regular,
    color: AppColors.inputText,
  );

  /// Input field hint style
  static const TextStyle inputHint = TextStyle(
    fontFamily: fontFamily,
    fontSize: 16,
    fontWeight: regular,
    color: AppColors.inputHint,
  );

  // ============================================================================
  // STATUS TEXT STYLES (Consolidated from text_styles.dart)
  // ============================================================================

  /// Error text style
  static const TextStyle error = TextStyle(
    fontFamily: fontFamily,
    fontSize: 12,
    fontWeight: regular,
    color: AppColors.error,
  );

  /// Success text style
  static const TextStyle success = TextStyle(
    fontFamily: fontFamily,
    fontSize: 12,
    fontWeight: regular,
    color: AppColors.success,
  );

  // ============================================================================
  // TIMER TEXT STYLES (Consolidated from text_styles.dart)
  // ============================================================================

  /// Large timer text style
  static const TextStyle timerLarge = TextStyle(
    fontFamily: fontFamily,
    fontSize: 48,
    fontWeight: light,
    color: AppColors.timerActive,
    fontFeatures: [FontFeature.tabularFigures()],
  );

  /// Medium timer text style
  static const TextStyle timerMedium = TextStyle(
    fontFamily: fontFamily,
    fontSize: 32,
    fontWeight: regular,
    color: AppColors.timerActive,
    fontFeatures: [FontFeature.tabularFigures()],
  );

  // ============================================================================
  // NAVIGATION TEXT STYLES (Consolidated from text_styles.dart)
  // ============================================================================

  /// Active navigation text style
  static const TextStyle navigationActive = TextStyle(
    fontFamily: fontFamily,
    fontSize: 12,
    fontWeight: semiBold,
    color: AppColors.navigationSelected,
  );

  /// Inactive navigation text style
  static const TextStyle navigationInactive = TextStyle(
    fontFamily: fontFamily,
    fontSize: 12,
    fontWeight: regular,
    color: AppColors.navigationUnselected,
  );

  // ============================================================================
  // CODE INPUT STYLE (Consolidated from text_styles.dart)
  // ============================================================================

  /// Code input text style
  static const TextStyle codeInput = TextStyle(
    fontFamily: fontFamily,
    fontSize: 24,
    fontWeight: semiBold,
    color: AppColors.onBackground,
    fontFeatures: [FontFeature.tabularFigures()],
  );

  /// Get the complete text theme
  static TextTheme get textTheme => const TextTheme(
    displayLarge: displayLarge,
    displayMedium: displayMedium,
    displaySmall: displaySmall,
    headlineLarge: headlineLarge,
    headlineMedium: headlineMedium,
    headlineSmall: headlineSmall,
    titleLarge: titleLarge,
    titleMedium: titleMedium,
    titleSmall: titleSmall,
    labelLarge: labelLarge,
    labelMedium: labelMedium,
    labelSmall: labelSmall,
    bodyLarge: bodyLarge,
    bodyMedium: bodyMedium,
    bodySmall: bodySmall,
  );

  // ============================================================================
  // RESPONSIVE TEXT STYLE METHODS
  // ============================================================================

  /// Get responsive tagline style
  static TextStyle responsiveTagline(BuildContext context) {
    return tagline.copyWith(fontSize: context.responsiveFont(32));
  }

  /// Get responsive headline large style
  static TextStyle responsiveHeadlineLarge(BuildContext context) {
    return headlineLarge.copyWith(fontSize: context.responsiveFont(32));
  }

  /// Get responsive body large style
  static TextStyle responsiveBodyLarge(BuildContext context) {
    return bodyLarge.copyWith(fontSize: context.responsiveFont(16));
  }

  /// Get responsive button large style
  static TextStyle responsiveButtonLarge(BuildContext context) {
    return buttonLarge.copyWith(
      fontSize: context.responsiveFont(16, scaleFactor: 1.1),
    );
  }

  /// Get responsive subtitle style
  static TextStyle subtitle(BuildContext context) {
    return TextStyle(
      fontFamily: fontFamily,
      fontSize: context.responsiveFont(16),
      fontWeight: regular,
      height: 1.4,
      color: AppColors.textSecondary,
    );
  }

  /// Get responsive logo style
  static TextStyle logo(BuildContext context) {
    return TextStyle(
      fontFamily: fontFamily,
      fontSize: context.responsiveFont(24),
      fontWeight: light,
      color: AppColors.textOnDark,
      letterSpacing: 2,
    );
  }

  /// Get responsive display large style (from text_styles.dart)
  static TextStyle responsiveDisplayLarge(BuildContext context) {
    final baseFontSize = 57.0;
    final responsiveFontSize = context.responsiveFont(baseFontSize);
    return displayLarge.copyWith(fontSize: responsiveFontSize);
  }

  /// Get responsive headline medium style (from text_styles.dart)
  static TextStyle responsiveHeadlineMedium(BuildContext context) {
    final baseFontSize = 28.0;
    final responsiveFontSize = context.responsiveFont(baseFontSize);
    return headlineMedium.copyWith(fontSize: responsiveFontSize);
  }

  /// Get responsive timer large style (from text_styles.dart)
  static TextStyle responsiveTimerLarge(BuildContext context) {
    final baseFontSize = 48.0;
    final responsiveFontSize = context.responsiveFont(
      baseFontSize,
      scaleFactor: 1.3,
    );
    return timerLarge.copyWith(fontSize: responsiveFontSize);
  }

  /// Get responsive button primary style (from text_styles.dart)
  static TextStyle responsiveButtonPrimary(BuildContext context) {
    final baseFontSize = 16.0;
    final responsiveFontSize = context.responsiveFont(
      baseFontSize,
      scaleFactor: 1.1,
    );
    return buttonPrimary.copyWith(fontSize: responsiveFontSize);
  }

  // ============================================================================
  // TIME PICKER STYLES (Responsive)
  // ============================================================================

  /// Large time display style (responsive)
  static TextStyle responsiveTimeLarge(BuildContext context) {
    final baseFontSize = 60.0;
    final responsiveFontSize = context.responsiveFont(
      baseFontSize,
      scaleFactor: 1.2,
    );
    return TextStyle(
      fontFamily: fontFamily,
      fontWeight: regular,
      fontSize: responsiveFontSize,
      height: 19.6 / baseFontSize,
      letterSpacing: 0,
      color: AppColors.onBackground,
    );
  }

  /// Small time display style (responsive)
  static TextStyle responsiveTimeSmall(BuildContext context) {
    final baseFontSize = 11.0;
    final responsiveFontSize = context.responsiveFont(
      baseFontSize,
      scaleFactor: 1.1,
    );
    return TextStyle(
      fontFamily: fontFamily,
      fontWeight: regular,
      fontSize: responsiveFontSize,
      height: 19.6 / baseFontSize,
      letterSpacing: 0,
      color: AppColors.onBackground,
    );
  }

  /// Day selector center style (responsive)
  static TextStyle responsiveDayCenter(BuildContext context) {
    final baseFontSize = 17.0;
    final responsiveFontSize = context.responsiveFont(
      baseFontSize,
      scaleFactor: 1.1,
    );
    return TextStyle(
      fontFamily: fontFamily,
      fontWeight: regular,
      fontSize: responsiveFontSize,
      height: 19.6 / baseFontSize,
      color: AppColors.onBackground,
    );
  }

  /// Day selector side style (responsive)
  static TextStyle responsiveDaySide(BuildContext context) {
    final baseFontSize = 11.0;
    final responsiveFontSize = context.responsiveFont(
      baseFontSize,
      scaleFactor: 1.1,
    );
    return TextStyle(
      fontFamily: fontFamily,
      fontWeight: regular,
      fontSize: responsiveFontSize,
      height: 19.6 / baseFontSize,
      color: AppColors.onBackground,
    );
  }

  // ============================================================================
  // STANDARDIZED LAYOUT TEXT STYLES
  // ============================================================================

  /// Title style for auth/setup screens
  /// Font: Sana Sans VAR, Weight: 400, Size: 40px, Line height: 38px, Letter spacing: -1px
  static TextStyle authTitle(BuildContext context) {
    return TextStyle(
      fontFamily: fontFamily,
      fontSize: context.responsiveFont(40),
      fontWeight: regular,
      height: 38 / 40, // Line height / font size
      letterSpacing: -1,
      color: AppColors.titleText, // Updated to use new color constant
    );
  }

  static TextStyle templateTitle(BuildContext context) {
    return TextStyle(
      fontFamily: fontFamily,
      fontSize: context.responsiveFont(30),
      fontWeight: regular,
      height: 36 / 30, // Line height / font size
      letterSpacing: -1,
      color: AppColors.titleText, // Updated to use new color constant
    );
  }

  /// Description style for auth/setup screens
  /// Font: Sana Sans VAR, Weight: 400, Size: 14px, Line height: 19.6px, Letter spacing: 0px, Color: #FFFFFF
  static TextStyle authDescription(BuildContext context) {
    return TextStyle(
      fontFamily: fontFamily,
      fontSize: context.responsiveFont(14),
      fontWeight: regular,
      height: 19.6 / 14, // Line height / font size
      letterSpacing: 0,
      color: AppColors.descriptionText, // Updated to use new color constant
    );
  }

  /// Button text style for auth/setup screens
  /// Font: Sana Sans VAR, Weight: 400, Size: 13.13px, Line height: 19.6px, Letter spacing: 0px, Color: #1F1F1F
  static TextStyle authButtonText(BuildContext context) {
    return TextStyle(
      fontFamily: fontFamily,
      fontSize: context.responsiveFont(13.13),
      fontWeight: regular,
      height: 19.6 / 13.13, // Line height / font size
      letterSpacing: 0,
      color: AppColors.buttonPrimaryText, // Updated to use new color constant
    );
  }

  /// Clickable text style for auth/setup screens
  /// Font: Sana Sans VAR, Weight: 400, Size: 13.13px, Line height: 19.6px, Letter spacing: 0px, Color: #FFFFFF
  static TextStyle authClickableText(BuildContext context) {
    return TextStyle(
      fontFamily: fontFamily,
      fontSize: context.responsiveFont(13.13),
      fontWeight: regular,
      height: 19.6 / 13.13, // Line height / font size
      letterSpacing: 0,
      color: const Color(0xFFFFFFFF),
    );
  }
}
