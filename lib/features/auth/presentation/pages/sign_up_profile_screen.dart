import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:go_router/go_router.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:safea/core/router/route_names.dart';
import 'package:safea/core/theme/app_text_styles.dart';
import 'package:safea/core/theme/colors.dart';
import 'package:safea/shared/extensions/string_hardcoded.dart';
import 'package:safea/shared/widgets/app_layout_template.dart';
import 'package:safea/shared/widgets/text_widget.dart';

/// Profile image upload screen
class SignUpProfileScreen extends HookConsumerWidget {
  const SignUpProfileScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final hasProfileImage = useState(false);
    return AppLayoutTemplate(
      title: 'Gib deinem\nProfil ein Gesicht'.hardcoded,
      titleTextStyle: AppTextStyles.templateTitle(context),
      showBackButton: true,
      content: Column(
        children: [
          const SizedBox(height: 40),
          Expanded(child: _buildProfileSection(hasProfileImage)),
          const SizedBox(height: 32),
        ],
      ),
      description:
          'Menschen in Not sehen dein Bild,\num dich besser zu erkennen.'
              .hardcoded,
      primaryButtonText: 'Bild als Profilbild festlegen'.hardcoded,
      secondaryClickableText: '',
      onPrimaryPressed: () => _handleSetProfileImage(context),
      ellipseSizeFactor: 0.4,
    );
  }

  Widget _buildProfileSection(ValueNotifier<bool> hasProfileImage) {
    return LayoutBuilder(
      builder: (context, constraints) {
        return Column(
          children: [
            // Main glow effect taking most of the space
            const Spacer(),
            const Expanded(
              flex: 4,
              child: Center(
                child: Icon(
                  Icons.cloud_upload_outlined,
                  color: AppColors.onSurfaceVariant,
                  size: 32,
                ),
              ),
            ),
            // Upload button
            GestureDetector(
              onTap: () => _handleImageUpload(hasProfileImage),
              child: Container(
                padding: const EdgeInsets.symmetric(
                  horizontal: 24,
                  vertical: 12,
                ),
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(25),
                  color: AppColors.onBackground.withAlpha(12),
                ),
                child: TextWidget(
                  'Bild hochladen'.hardcoded,
                  style: AppTextStyles.bodyMedium.copyWith(
                    color: AppColors.onBackground,
                  ),
                ),
              ),
            ),

            const SizedBox(height: 8),

            // Description text
            TextWidget(
              'Verwende ein gut erkennbares Bild\ndeines Gesichts.'.hardcoded,
              style: AppTextStyles.bodyMedium.copyWith(
                color: AppColors.onSurfaceVariant,
              ),
              textAlign: TextAlign.center,
            ),

            const SizedBox(height: 24),
          ],
        );
      },
    );
  }

  void _handleImageUpload(ValueNotifier<bool> hasProfileImage) {
    // For MVP, simulate image upload
    hasProfileImage.value = true;
  }

  void _handleSetProfileImage(BuildContext context) {
    // For MVP, always proceed to verification
    context.push(RouteNames.verification);
  }
}
