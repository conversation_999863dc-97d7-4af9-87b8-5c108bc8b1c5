import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:safea/core/router/route_names.dart';
import 'package:safea/core/theme/app_text_styles.dart';
import 'package:safea/shared/extensions/string_hardcoded.dart';
import 'package:safea/shared/widgets/app_layout_template.dart';

class StartScreen extends StatelessWidget {
  const StartScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return AppLayoutTemplate(
      content: const SizedBox.shrink(),
      title: 'Ein Klick.\nSofort helfen.\nSicherheit bieten.'.hardcoded,
      description:
          '<PERSON><PERSON><PERSON> die App, um Menschen in kritischen\n'
                  'Momenten zu helfen. Erkenne Gefahr,\n'
                  'greife ein und biete Unterstützung –\n'
                  'schnell und unkompliziert.'
              .hardcoded,
      primaryButtonText: 'Jetzt als Privatperson anmelden'.hardcoded,
      onPrimaryPressed: () => context.push(RouteNames.signUp),
      secondaryClickableText: 'Al<PERSON> Unternehmen'.hardcoded,
      onSecondaryTextTap: () => context.push(RouteNames.signUp),
      showEllipseLogo: true,
      ellipseLogoText: 'safea',
      ellipseLogoStyle: AppTextStyles.appLogoText,
      ellipseIntensity: 5,
    );
  }
}
