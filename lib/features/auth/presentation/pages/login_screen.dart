import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:safea/core/router/route_names.dart';
import 'package:safea/core/theme/app_text_styles.dart';
import 'package:safea/core/theme/colors.dart';
import 'package:safea/core/theme/responsive_extensions.dart';
import 'package:safea/shared/extensions/string_hardcoded.dart';
import 'package:safea/shared/widgets/app_layout_template.dart';
import 'package:safea/shared/widgets/app_scaffold.dart';
import 'package:safea/shared/widgets/app_text_field.dart';
import 'package:safea/shared/widgets/text_widget.dart';

/// Login screen
class LoginScreen extends StatefulWidget {
  const LoginScreen({super.key});

  @override
  State<LoginScreen> createState() => _LoginScreenState();
}

class _LoginScreenState extends State<LoginScreen> {
  final _formKey = GlobalKey<FormState>();
  final _emailController = TextEditingController();

  @override
  void initState() {
    super.initState();
    // Pre-fill with mock data for MVP
    _emailController.text = '<EMAIL>';
  }

  @override
  void dispose() {
    _emailController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AppScaffold(
      body: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 8),
            child: IconButton(
              icon: const Icon(
                Icons.arrow_back_sharp,
                color: AppColors.onBackground,
                size: 24,
              ),
              onPressed: () => context.pop(),
            ),
          ),
          _buildHeader(),
          Expanded(child: _buildForm()),
          _buildBottomSection(),
        ],
      ),
    );
  }

  Widget _buildHeader() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 16),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          TextWidget(
            'Anmelden'.hardcoded,
            style: AppTextStyles.authTabs.copyWith(
              color: AppColors.activeTab, // Active tab color
            ),
          ),
          GestureDetector(
            onTap: () => context.pushReplacement(RouteNames.signUp),
            child: TextWidget(
              'Mitmachen'.hardcoded,
              style: AppTextStyles.authTabs.copyWith(
                color: AppColors.passiveTab, // Passive tab color
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildForm() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(24),
      child: Form(
        key: _formKey,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const SizedBox(height: 40),
            AppTextField(
              controller: _emailController,
              label: 'Deine E-Mail-Adresse*'.hardcoded,
              keyboardType: TextInputType.emailAddress,
              isRequired: true,
              showRequiredIndicator: true,
              validator: (value) {
                if (value?.isEmpty ?? true) {
                  return 'E-Mail-Adresse ist erforderlich'.hardcoded;
                }
                return null;
              },
            ),
            const SizedBox(height: 200), // Spacer to match design
          ],
        ),
      ),
    );
  }

  Widget _buildBottomSection() {
    return Padding(
      padding: const EdgeInsets.all(24),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          TextWidget(
            'Öffne die App, um Menschen in kritischen\nMomenten zu helfen. Erkenne Gefahr,\ngreife ein und biete Unterstützung –\nschnell und unkompliziert.'
                .hardcoded,
            style: AppTextStyles.bodyMedium.copyWith(
              color: AppColors.textInverse,
            ),
          ),
          const SizedBox(height: 16),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Expanded(
                flex: 2,
                child: AppButton(
                  text: 'Jetzt als Privatperson anmelden'.hardcoded,
                  onPressed: _handleLogin,
                ),
              ),
              SizedBox(width: context.spacingXs),
              Expanded(
                child: GestureDetector(
                  onTap: _handleLogin,
                  child: TextWidget(
                    'Als Unternehmen'.hardcoded,
                    style: AppTextStyles.authClickableText(context),
                    textAlign: TextAlign.end,
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  void _handleLogin() {
    // For MVP, always proceed to verification
    context.push(RouteNames.verification);
  }
}
