import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:go_router/go_router.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:safea/core/router/route_names.dart';
import 'package:safea/core/theme/app_text_styles.dart';
import 'package:safea/core/theme/colors.dart';
import 'package:safea/shared/extensions/string_hardcoded.dart';
import 'package:safea/shared/widgets/app_layout_template.dart';
import 'package:safea/shared/widgets/app_scaffold.dart';
import 'package:safea/shared/widgets/app_text_field.dart';
import 'package:safea/shared/widgets/text_widget.dart';

/// Sign up screen with form fields
class SignUpScreen extends HookConsumerWidget {
  const SignUpScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final formKey = useMemoized(GlobalKey<FormState>.new, []);
    final emailController = useTextEditingController(
      text: '<EMAIL>',
    );
    final firstNameController = useTextEditingController(text: 'Max');
    final lastNameController = useTextEditingController(text: 'Mustermann');
    final streetController = useTextEditingController(
      text: 'Maximilian-Straße 19a',
    );
    final postalCodeController = useTextEditingController(text: '09192');
    final cityController = useTextEditingController(text: 'München');
    final countryController = useTextEditingController(text: 'Deutschland');
    return AppScaffold(
      body: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 8),
            child: IconButton(
              icon: const Icon(
                Icons.arrow_back_sharp,
                color: AppColors.onBackground,
                size: 24,
              ),
              onPressed: () => context.pop(),
            ),
          ),
          _buildHeader(context),
          Expanded(
            child: _buildFormWithButton(
              formKey,
              emailController,
              firstNameController,
              lastNameController,
              streetController,
              postalCodeController,
              cityController,
              countryController,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildHeader(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 16),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          TextWidget(
            'Mitmachen'.hardcoded,
            style: AppTextStyles.authTabs.copyWith(
              color: AppColors.activeTab, // Active tab color
            ),
          ),
          GestureDetector(
            onTap: () => context.pushReplacement(RouteNames.login),
            child: TextWidget(
              'Anmelden'.hardcoded,
              style: AppTextStyles.authTabs.copyWith(
                color: AppColors.passiveTab, // Passive tab color
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFormWithButton(
    GlobalKey<FormState> formKey,
    TextEditingController emailController,
    TextEditingController firstNameController,
    TextEditingController lastNameController,
    TextEditingController streetController,
    TextEditingController postalCodeController,
    TextEditingController cityController,
    TextEditingController countryController,
  ) {
    return Stack(
      children: [
        // Scrollable Form
        Positioned.fill(
          child: SingleChildScrollView(
            padding: const EdgeInsets.fromLTRB(
              24,
              24,
              24,
              100,
            ), // Altta butona yer bırak
            child: Form(
              key: formKey,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  AppTextField(
                    controller: emailController,
                    label: 'Deine E-Mail-Adresse*'.hardcoded,
                    keyboardType: TextInputType.emailAddress,
                    isRequired: true,
                    showRequiredIndicator: true,
                    validator: (value) {
                      if (value?.isEmpty ?? true) {
                        return 'E-Mail-Adresse ist erforderlich'.hardcoded;
                      }
                      return null;
                    },
                  ),
                  const SizedBox(height: 24),
                  AppTextField(
                    controller: firstNameController,
                    label: 'Dein Vorname*'.hardcoded,
                    isRequired: true,
                    showRequiredIndicator: true,
                    validator: (value) {
                      if (value?.isEmpty ?? true) {
                        return 'Vorname ist erforderlich'.hardcoded;
                      }
                      return null;
                    },
                  ),
                  const SizedBox(height: 24),
                  AppTextField(
                    controller: lastNameController,
                    label: 'Dein Nachname*'.hardcoded,
                    isRequired: true,
                    showRequiredIndicator: true,
                    validator: (value) {
                      if (value?.isEmpty ?? true) {
                        return 'Nachname ist erforderlich'.hardcoded;
                      }
                      return null;
                    },
                  ),
                  const SizedBox(height: 24),
                  AppTextField(
                    controller: streetController,
                    label: 'Deine Straße*'.hardcoded,
                    isRequired: true,
                    showRequiredIndicator: true,
                    validator: (value) {
                      if (value?.isEmpty ?? true) {
                        return 'Straße ist erforderlich'.hardcoded;
                      }
                      return null;
                    },
                  ),
                  const SizedBox(height: 24),
                  AppTextField(
                    controller: postalCodeController,
                    label: 'Postleitzahl*'.hardcoded,
                    keyboardType: TextInputType.number,
                    isRequired: true,
                    showRequiredIndicator: true,
                    validator: (value) {
                      if (value?.isEmpty ?? true) {
                        return 'Postleitzahl ist erforderlich'.hardcoded;
                      }
                      return null;
                    },
                  ),
                  const SizedBox(height: 24),
                  AppTextField(
                    controller: cityController,
                    label: 'Stadtname*'.hardcoded,
                    isRequired: true,
                    showRequiredIndicator: true,
                    validator: (value) {
                      if (value?.isEmpty ?? true) {
                        return 'Stadtname ist erforderlich'.hardcoded;
                      }
                      return null;
                    },
                  ),
                  const SizedBox(height: 24),
                  AppTextField(
                    controller: countryController,
                    label: 'Land*'.hardcoded,
                    isRequired: true,
                    showRequiredIndicator: true,
                    validator: (value) {
                      if (value?.isEmpty ?? true) {
                        return 'Land ist erforderlich'.hardcoded;
                      }
                      return null;
                    },
                  ),
                  const SizedBox(height: 32),
                ],
              ),
            ),
          ),
        ),

        // Floating Button
        Container(
          padding: const EdgeInsets.all(24),
          child: Align(
            alignment: Alignment.bottomCenter,
            child: SizedBox(
              child: Builder(
                builder: (context) => AppButton(
                  text: 'Jetzt Verifzieren'.hardcoded,
                  onPressed: () => _handleSignUp(context),
                ),
              ),
            ),
          ),
        ),
      ],
    );
  }

  void _handleSignUp(BuildContext context) {
    // For MVP, always proceed regardless of validation
    context.push(RouteNames.signUpProfile);
  }
}
