import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:go_router/go_router.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:safea/core/router/route_names.dart';
import 'package:safea/core/theme/app_text_styles.dart';
import 'package:safea/shared/widgets/app_layout_template.dart';

class SplashScreen extends HookConsumerWidget {
  const SplashScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final animationController = useAnimationController(
      duration: const Duration(seconds: 2),
    );

    final fadeAnimation = useMemoized(
      () => Tween<double>(begin: 0, end: 1).animate(
        CurvedAnimation(
          parent: animationController,
          curve: const Interval(0, 0.6, curve: Curves.easeIn),
        ),
      ),
      [animationController],
    );

    final scaleAnimation = useMemoized(
      () => Tween<double>(begin: 0.8, end: 1).animate(
        CurvedAnimation(
          parent: animationController,
          curve: const Interval(0.2, 0.8, curve: Curves.elasticOut),
        ),
      ),
      [animationController],
    );

    useEffect(() {
      animationController.forward();
      //TODO(Ali): s=>critical, u=>ali, m=> Remove this part. It is only to see splash screen at beginning.
      // Navigate to start screen after 3 seconds
      Future.delayed(const Duration(seconds: 3), () {
        if (context.mounted) {
          context.go(RouteNames.start);
        }
      });

      return () {
        // Cleanup is handled automatically by useAnimationController
      };
    }, [animationController]);
    return AnimatedBuilder(
      animation: animationController,
      builder: (context, child) {
        return FadeTransition(
          opacity: fadeAnimation,
          child: ScaleTransition(
            scale: scaleAnimation,
            child: const AppLayoutTemplate(
              content: SizedBox.shrink(),
              showTitle: false,
              showEllipseLogo: true,
              ellipseLogoText: 'safea',
              ellipseLogoStyle: AppTextStyles.appLogoText,
              ellipseIntensity: 1.5,
            ),
          ),
        );
      },
    );
  }
}
