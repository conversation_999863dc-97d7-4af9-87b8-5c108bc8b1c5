import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:go_router/go_router.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:safea/core/router/route_names.dart';
import 'package:safea/core/theme/app_text_styles.dart';
import 'package:safea/core/theme/colors.dart';
import 'package:safea/shared/extensions/string_hardcoded.dart';
import 'package:safea/shared/widgets/app_layout_template.dart';
import 'package:safea/shared/widgets/text_widget.dart';

/// Email/Phone verification screen with code input
class VerificationScreen extends HookConsumerWidget {
  const VerificationScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final controllers = useMemoized(
      () => List.generate(4, (index) => TextEditingController()),
      [],
    );
    final focusNodes = useMemoized(
      () => List.generate(4, (index) => FocusNode()),
      [],
    );
    final timerController = useAnimationController(
      duration: const Duration(seconds: 30),
    );
    final remainingSeconds = useState(30);

    useEffect(() {
      void listener() {
        remainingSeconds.value = (30 * (1 - timerController.value)).round();
      }

      timerController
        ..addListener(listener)
        ..forward();

      return () {
        timerController.removeListener(listener);
        for (final controller in controllers) {
          controller.dispose();
        }
        for (final focusNode in focusNodes) {
          focusNode.dispose();
        }
      };
    }, [timerController]);
    return AppLayoutTemplate(
      showBackButton: true,
      title: 'Bestätige dein Konto'.hardcoded,
      titleTextStyle: AppTextStyles.authTabs.copyWith(
        color: AppColors.onBackground,
      ),
      content: Column(
        crossAxisAlignment: CrossAxisAlignment.end,
        children: [
          const SizedBox(height: 40),
          _buildCodeInput(controllers, focusNodes),
          const SizedBox(height: 24),
          _buildTimer(remainingSeconds),
          const SizedBox(height: 60),
        ],
      ),
      description:
          'Der Bestätigungscode wurde an deine\nE-Mail-Adresse gesendet.'
              .hardcoded,
      primaryButtonText: 'Setup starten'.hardcoded,
      onPrimaryPressed: () => _handleVerification(context),
      animateEllipse: false,
      ellipseSizeFactor: 0,
      ellipseIntensity: 0,
    );
  }

  Widget _buildCodeInput(
    List<TextEditingController> controllers,
    List<FocusNode> focusNodes,
  ) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        TextWidget(
          'Füge den Code ein*'.hardcoded,
          style: AppTextStyles.bodyMedium.copyWith(
            color: AppColors.onSurfaceVariant,
          ),
        ),
        const SizedBox(height: 32),
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceEvenly,
          children: List.generate(4, (index) {
            return SizedBox(
              width: 50,
              child: TextField(
                controller: controllers[index],
                focusNode: focusNodes[index],
                textAlign: TextAlign.center,
                style: AppTextStyles.codeInput.copyWith(
                  color: AppColors.onBackground,
                  fontSize: 32,
                  fontWeight: FontWeight.w300,
                ),
                keyboardType: TextInputType.number,
                maxLength: 1,
                decoration: const InputDecoration(
                  counterText: '',
                  border: UnderlineInputBorder(
                    borderSide: BorderSide(color: AppColors.outline),
                  ),
                  enabledBorder: UnderlineInputBorder(
                    borderSide: BorderSide(color: AppColors.outline),
                  ),
                  focusedBorder: UnderlineInputBorder(
                    borderSide: BorderSide(
                      color: AppColors.primaryColor,
                      width: 2,
                    ),
                  ),
                  contentPadding: EdgeInsets.only(bottom: 8),
                ),
                onChanged: (value) {
                  if (value.isNotEmpty && index < 3) {
                    focusNodes[index + 1].requestFocus();
                  } else if (value.isEmpty && index > 0) {
                    focusNodes[index - 1].requestFocus();
                  }

                  // Check if all fields are filled
                  if (controllers.every((c) => c.text.isNotEmpty)) {
                    // Handle verification logic here
                  }
                },
              ),
            );
          }),
        ),
      ],
    );
  }

  Widget _buildTimer(ValueNotifier<int> remainingSeconds) {
    return ValueListenableBuilder<int>(
      valueListenable: remainingSeconds,
      builder: (context, seconds, child) {
        return TextWidget(
          '0:${seconds.toString().padLeft(2, '0')} Code erneut schicken'
              .hardcoded,
          style: AppTextStyles.bodyMedium.copyWith(
            color: seconds > 0 ? AppColors.success : AppColors.onSurfaceVariant,
          ),
        );
      },
    );
  }

  void _handleVerification(BuildContext context) {
    // For MVP, always proceed to setup
    context.go(RouteNames.setup01);
  }
}
