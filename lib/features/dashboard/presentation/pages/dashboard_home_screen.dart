import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:go_router/go_router.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:safea/core/router/route_names.dart';
import 'package:safea/core/theme/app_text_styles.dart';
import 'package:safea/core/theme/colors.dart';
import 'package:safea/core/theme/responsive_extensions.dart';
import 'package:safea/features/dashboard/presentation/pages/dashboard_user_done_screen.dart';
import 'package:safea/features/dashboard/presentation/pages/dashboard_user_tracking_screen.dart';
import 'package:safea/shared/extensions/string_hardcoded.dart';
import 'package:safea/shared/widgets/app_layout_template.dart';
import 'package:safea/shared/widgets/text_widget.dart';

/// Dashboard home screen with swipe functionality
class DashboardHomeScreen extends HookConsumerWidget {
  const DashboardHomeScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final pageController = usePageController();
    final currentPage = useState(0);

    return PageView(
      controller: pageController,
      onPageChanged: (index) => currentPage.value = index,
      children: [
        // Dashboard Home (current screen)
        _buildDashboardHome(context),
        // Dashboard User Tracking
        const DashboardUserTrackingScreen(),
        // Dashboard User Done
        const DashboardUserDoneScreen(),
      ],
    );
  }

  Widget _buildDashboardHome(BuildContext context) {
    return AppLayoutTemplate(
      showTitle: false,
      ellipseLogoStyle: AppTextStyles.appLogoText,
      ellipseIntensity: 5,
      content: Column(
        children: [
          const Spacer(flex: 3),
          _buildCenterContent(),
          const Spacer(flex: 3),
          _buildBottomNavigation(context),
        ],
      ),
    );
  }

  Widget _buildCenterContent() {
    return Column(
      children: [
        // Safea logo text
        TextWidget('safea'.hardcoded, style: AppTextStyles.appLogoText),
        const SizedBox(height: 16),
        // Main status text
        TextWidget(
          'Kein Notruf'.hardcoded,
          style: AppTextStyles.dashboardTabs.copyWith(
            color: AppColors.onPrimary,
          ),
        ),
        const SizedBox(height: 8),
        // Subtitle
        TextWidget(
          'zurzeit'.hardcoded,
          style: AppTextStyles.bodySmall.copyWith(color: AppColors.onPrimary),
        ),
      ],
    );
  }

  Widget _buildBottomNavigation(BuildContext context) {
    return Align(
      child: Container(
        decoration: BoxDecoration(
          color: AppColors.backgroundBlack,
          borderRadius: BorderRadius.circular(30),
        ),
        padding: EdgeInsets.symmetric(
          horizontal: context.responsiveSpacing(8),
          vertical: context.responsiveSpacing(2),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            // History/Clock icon
            IconButton(
              onPressed: () {
                // TODO: Navigate to history
              },
              icon: const Icon(
                Icons.watch_later_outlined,
                color: Colors.white,
                size: 28,
              ),
            ),

            // Settings icon
            IconButton(
              onPressed: () {
                context.push(RouteNames.settings);
              },
              icon: const Icon(
                Icons.settings_outlined,
                color: Colors.white,
                size: 28,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
