import 'package:flutter/material.dart';

import 'package:safea/core/theme/app_text_styles.dart';
import 'package:safea/core/theme/colors.dart';
import 'package:safea/shared/extensions/string_hardcoded.dart';
import 'package:safea/shared/widgets/app_button.dart';
import 'package:safea/shared/widgets/app_layout_template.dart' hide AppButton;
import 'package:safea/shared/widgets/text_widget.dart';

/// Dashboard user tracking screen with timer display
class DashboardUserTrackingScreen extends StatelessWidget {
  const DashboardUserTrackingScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return AppLayoutTemplate(
      showTitle: false,
      content: Align(
        child: Column(
          spacing: 24,
          mainAxisAlignment: MainAxisAlignment.end,
          children: [_buildUserInfo(), _builButtons(context)],
        ),
      ),
      glowColor: AppColors.ellipseRed,
      profileImage:
          'https://images.unsplash.com/photo-1494790108755-2616b612b47c?w=400&h=400&fit=crop&crop=face',
      showProfileImage: true,
    );
  }

  Widget _buildUserInfo() {
    return Column(
      spacing: 8,
      children: [
        // Name
        TextWidget(
          'Magdalena'.hardcoded,
          style: AppTextStyles.dashboardTabs.copyWith(
            color: AppColors.onBackground,
          ),
        ),

        // Status text
        TextWidget(
          'wird verfolgt'.hardcoded,
          style: AppTextStyles.stateText.copyWith(
            color: AppColors.onBackground,
          ),
        ),
      ],
    );
  }

  Widget _builButtons(BuildContext context) {
    return Column(
      spacing: 8,
      children: [
        AppButton(
          text: 'Hilfe leisten - 0.5 km entfernt'.hardcoded,
          onPressed: () {},
          textStyle: AppTextStyles.authButtonText(
            context,
          ).copyWith(color: AppColors.textPrimary),
        ),
        AppButton(
          text: 'Keine Zeit'.hardcoded,
          onPressed: () {},
          type: AppButtonType.text,
          textStyle: AppTextStyles.authButtonText(
            context,
          ).copyWith(color: AppColors.buttonDashboard),
        ),
      ],
    );
  }
}
