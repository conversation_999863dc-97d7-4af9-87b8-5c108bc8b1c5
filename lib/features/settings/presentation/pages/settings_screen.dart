import 'dart:ui';
import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:go_router/go_router.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:safea/core/theme/app_text_styles.dart';
import 'package:safea/core/theme/colors.dart';
import 'package:safea/shared/extensions/string_hardcoded.dart';
import 'package:safea/shared/widgets/app_button.dart';
import 'package:safea/shared/widgets/app_layout_template.dart' hide AppButton;
import 'package:safea/shared/widgets/app_text_field.dart';
import 'package:safea/shared/widgets/text_widget.dart';

/// Settings screen matching the German design mockup exactly
class SettingsScreen extends HookConsumerWidget {
  const SettingsScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    // Form controllers
    final emailController = useTextEditingController(
      text: '<EMAIL>',
    );
    final firstNameController = useTextEditingController(text: 'Max');
    final lastNameController = useTextEditingController(text: 'Mustermann');
    final streetController = useTextEditingController(
      text: 'Maximilian Straße 19a',
    );
    final postalCodeController = useTextEditingController(text: '09192');
    final cityController = useTextEditingController(text: 'München');
    final countryController = useTextEditingController(text: 'Deutschland');

    // Toggle states
    final trackingEnabled = useState(true);
    final availabilityEnabled = useState(true);

    // Delete profile dialog state
    final showDeleteDialog = useState(false);

    return AppLayoutTemplate(
      showBackButton: true,
      title: 'Einstellungen'.hardcoded,
      titleTextStyle: AppTextStyles.authTabs.copyWith(
        color: AppColors.textOnDark,
      ),
      primaryButtonText: 'Aktualisieren'.hardcoded,
      onPrimaryPressed: () {
        context.pop();
      },
      secondaryClickableText: '',
      ellipseLogoStyle: AppTextStyles.appLogoText,
      ellipseIntensity: 0,
      ellipseSizeFactor: 0,
      content: _buildContent(
        emailController,
        firstNameController,
        lastNameController,
        streetController,
        postalCodeController,
        cityController,
        countryController,
        trackingEnabled,
        availabilityEnabled,
        showDeleteDialog,
      ),
    );
  }

  Widget _buildContent(
    TextEditingController emailController,
    TextEditingController firstNameController,
    TextEditingController lastNameController,
    TextEditingController streetController,
    TextEditingController postalCodeController,
    TextEditingController cityController,
    TextEditingController countryController,
    ValueNotifier<bool> trackingEnabled,
    ValueNotifier<bool> availabilityEnabled,
    ValueNotifier<bool> showDeleteDialog,
  ) {
    return SingleChildScrollView(
      padding: const EdgeInsets.symmetric(horizontal: 24),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const SizedBox(height: 16),

          // Profile update section with avatar
          Row(
            children: [
              Container(
                width: 50,
                height: 50,
                decoration: const BoxDecoration(
                  color: AppColors.primaryColor,
                  shape: BoxShape.circle,
                ),
                child: const Icon(
                  Icons.photo_library_outlined,
                  color: AppColors.onPrimary,
                  size: 24,
                ),
              ),
              const SizedBox(width: 16),
              TextWidget(
                'Profil aktualisieren'.hardcoded,
                style: AppTextStyles.bodyLarge.copyWith(
                  color: AppColors.onBackground,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ],
          ),

          // Delete profile dialog box
          if (showDeleteDialog.value) ...[
            const SizedBox(height: 16),
            _buildDeleteProfileDialog(showDeleteDialog),
          ],
          const SizedBox(height: 32),

          // Form fields
          AppTextField(
            controller: emailController,
            label: 'Deine E-Mail-Adresse*'.hardcoded,
            keyboardType: TextInputType.emailAddress,
            isRequired: true,
            showRequiredIndicator: true,
          ),
          const SizedBox(height: 24),

          AppTextField(
            controller: firstNameController,
            label: 'Dein Vorname*'.hardcoded,
            isRequired: true,
            showRequiredIndicator: true,
          ),
          const SizedBox(height: 24),

          AppTextField(
            controller: lastNameController,
            label: 'Dein Nachname*'.hardcoded,
            isRequired: true,
            showRequiredIndicator: true,
          ),
          const SizedBox(height: 24),

          AppTextField(
            controller: streetController,
            label: 'Deine Straße*'.hardcoded,
            isRequired: true,
            showRequiredIndicator: true,
          ),
          const SizedBox(height: 24),

          AppTextField(
            controller: postalCodeController,
            label: 'Postleitzahl*'.hardcoded,
            keyboardType: TextInputType.number,
            isRequired: true,
            showRequiredIndicator: true,
          ),
          const SizedBox(height: 24),

          AppTextField(
            controller: cityController,
            label: 'Stadtname*'.hardcoded,
            isRequired: true,
            showRequiredIndicator: true,
          ),
          const SizedBox(height: 24),

          AppTextField(
            controller: countryController,
            label: 'Land*'.hardcoded,
            isRequired: true,
            showRequiredIndicator: true,
          ),
          const SizedBox(height: 32),

          // Toggle switches section
          _buildToggleItem(
            'Tracking einschalten*'.hardcoded,
            'Aktiv'.hardcoded,
            trackingEnabled,
          ),
          const SizedBox(height: 24),

          _buildToggleItem(
            'Deine Verfügbarkeit*'.hardcoded,
            'Du bist aktuell verfügbar.'.hardcoded,
            availabilityEnabled,
          ),
          const SizedBox(height: 32),

          // Delete profile section
          RichText(
            text: TextSpan(
              style: AppTextStyles.bodyMedium.copyWith(
                color: AppColors.onSurfaceVariant,
              ),
              children: [
                TextSpan(text: 'Profil löschen'.hardcoded),
                const TextSpan(
                  text: '*',
                  style: TextStyle(color: AppColors.ellipseGreen),
                ),
              ],
            ),
          ),
          const SizedBox(height: 8),
          GestureDetector(
            onTap: () {
              showDeleteDialog.value = !showDeleteDialog.value;
            },
            child: TextWidget(
              'Klicke hier um dein Profil zu löschen.'.hardcoded,
              style: AppTextStyles.bodyMedium.copyWith(
                color: AppColors.textWarning,
              ),
            ),
          ),

          const SizedBox(height: 32),
        ],
      ),
    );
  }

  Widget _buildToggleItem(
    String title,
    String subtitle,
    ValueNotifier<bool> valueNotifier,
  ) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        RichText(
          text: TextSpan(
            style: AppTextStyles.bodyMedium.copyWith(
              color: AppColors.onSurfaceVariant,
            ),
            children: [
              TextSpan(text: title.replaceAll('*', '')),
              if (title.contains('*'))
                const TextSpan(
                  text: '*',
                  style: TextStyle(color: AppColors.ellipseGreen),
                ),
            ],
          ),
        ),
        const SizedBox(height: 8),
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            TextWidget(
              subtitle,
              style: AppTextStyles.bodyMedium.copyWith(
                color: AppColors.onBackground,
              ),
            ),
            Switch(
              value: valueNotifier.value,
              onChanged: (value) => valueNotifier.value = value,
              thumbColor: const WidgetStatePropertyAll<Color>(Colors.white),
              activeColor: AppColors.primaryColor,
              activeTrackColor: AppColors.primaryColor,
              inactiveThumbColor: AppColors.onSurfaceVariant,
              inactiveTrackColor: AppColors.onSurfaceVariant,
            ),
          ],
        ),
        const SizedBox(height: 16),
        // Line separator
        Container(
          height: 1,
          color: AppColors.onSurfaceVariant.withValues(alpha: 0.2),
        ),
      ],
    );
  }

  Widget _buildDeleteProfileDialog(ValueNotifier<bool> showDeleteDialog) {
    return ClipRRect(
      borderRadius: BorderRadius.circular(20),
      child: BackdropFilter(
        filter: ImageFilter.blur(sigmaX: 30, sigmaY: 30),
        child: Column(
          children: [
            Container(
              height: 222,
              width: double.infinity,
              padding: const EdgeInsets.fromLTRB(17, 24, 17, 24),
              decoration: BoxDecoration(
                color: const Color(0x0DFFFFFF), // #FFFFFF0D (5% opacity)
                borderRadius: BorderRadius.circular(20),
                border: Border.all(
                  color: const Color(0x26585858), // #58585826
                ),
                boxShadow: const [
                  BoxShadow(
                    color: Color(0x26000000), // #00000026
                    offset: Offset(0, 4),
                    blurRadius: 15,
                  ),
                ],
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  TextWidget(
                    'Profil löschen'.hardcoded,
                    style: AppTextStyles.bodyMedium.copyWith(
                      color: AppColors.onBackground,
                    ),
                  ),
                  const SizedBox(height: 10),
                  Expanded(
                    child: TextWidget(
                      'Schade, dass du dein Profil löschst – dieser Schritt ist unwiderruflich. Deine Daten werden gelöscht, du kannst niemandem mehr helfen.'
                          .hardcoded,
                      style: AppTextStyles.bodyMedium.copyWith(
                        color: AppColors.onBackground,
                      ),
                      maxLines: 4,
                    ),
                  ),
                  const SizedBox(height: 16),
                  AppButton(
                    text: 'Mein Profil löschen'.hardcoded,
                    buttonColor: AppColors.buttonRedBackground,
                    onPressed: () {
                      // Handle actual profile deletion
                      showDeleteDialog.value = false;
                    },
                    textStyle: AppTextStyles.hours.copyWith(
                      color: AppColors.buttonRedText,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
