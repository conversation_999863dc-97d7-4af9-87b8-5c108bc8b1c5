/* import 'package:flutter/material.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:safea/core/router/route_names.dart';
import 'package:safea/core/theme/app_text_styles.dart';
import 'package:safea/core/theme/colors.dart';
import 'package:safea/shared/extensions/string_hardcoded.dart';
import 'package:safea/shared/widgets/app_layout_template.dart';
import 'package:safea/shared/widgets/text_widget.dart';

/// Tracking setup screen with German design
class SetupTrackingScreen extends ConsumerWidget {
  const SetupTrackingScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return AppLayoutTemplate(
      showBackButton: true,
      title: 'Tracking einschalten'.hardcoded,
      titleTextStyle: AppTextStyles.authTabs.copyWith(
        color: AppColors.onBackground,
      ),
      description:
          'Tracking erlauben und aktivieren – gemeinsam ethisch sinnvoll helfen und unterstützen.'
              .hardcoded,
      content: _buildTrackingBenefits(),
      primaryButtonText: 'Tracking einschalten'.hardcoded,
      onPrimaryPressed: () => _handleEnableTracking(context),
      secondaryClickableText: '',
      animateEllipse: false,
      ellipseSizeFactor: 0,
      ellipseIntensity: 0,
    );
  }

  Widget _buildTrackingBenefits() {
    final benefits = [
      'Durch dein persönliches Tracking können wir dich benachrichtigen, wenn in deiner Nähe jemand Unterstützung braucht – so kannst du schnell helfen.'
          .hardcoded,
      'Deine Daten bleiben sicher. Wir verkaufen deine Daten nicht und geben sie nicht weiter. Wir engagieren uns ethisch, um mit dir die Welt sicherer zu machen.'
          .hardcoded,
    ];

    return ListView.builder(
      itemCount: benefits.length,
      itemBuilder: (context, index) {
        return Padding(
          padding: const EdgeInsets.only(bottom: 32),
          child: _buildBenefitItem(benefits[index], true),
        );
      },
    );
  }

  Widget _buildBenefitItem(String text, bool isEnabled) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Container(
          width: 20,
          height: 20,
          margin: const EdgeInsets.only(top: 2),
          decoration: BoxDecoration(
            color: isEnabled ? AppColors.success : AppColors.border,
            borderRadius: BorderRadius.circular(4),
          ),
          child: isEnabled
              ? const Icon(Icons.check, color: Colors.white, size: 14)
              : null,
        ),
        const SizedBox(width: 16),
        Expanded(
          child: TextWidget(
            text,
            style: AppTextStyles.bodyMedium.copyWith(
              color: AppColors.onBackground,
              height: 1.5,
            ),
            maxLines: 5,
          ),
        ),
      ],
    );
  }

  void _handleEnableTracking(BuildContext context) {
    // Navigate to available times setup
    context.push(RouteNames.setupAvailableTimes);
  }
}
 */

import 'package:flutter/material.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:safea/core/router/route_names.dart';
import 'package:safea/core/theme/app_text_styles.dart';
import 'package:safea/core/theme/colors.dart';
import 'package:safea/shared/extensions/string_hardcoded.dart';
import 'package:safea/shared/widgets/app_layout_template.dart';
import 'package:safea/shared/widgets/text_widget.dart';

/// Tracking setup screen with German design
class SetupTrackingScreen extends ConsumerStatefulWidget {
  const SetupTrackingScreen({super.key});

  @override
  ConsumerState<SetupTrackingScreen> createState() =>
      _SetupTrackingScreenState();
}

class _SetupTrackingScreenState extends ConsumerState<SetupTrackingScreen> {
  final List<bool> _consents = List.filled(2, false); // For 2 benefits

  @override
  Widget build(BuildContext context) {
    final allConsented = _consents.every((e) => e);

    return AppLayoutTemplate(
      showBackButton: true,
      title: 'Tracking einschalten'.hardcoded,
      titleTextStyle: AppTextStyles.authTabs.copyWith(
        color: AppColors.onBackground,
      ),
      description:
          'Tracking erlauben und aktivieren – \ngemeinsam ethisch sinnvoll helfen und unterstützen.'
              .hardcoded,
      content: _buildConsentList(),
      primaryButtonText: 'Tracking einschalten'.hardcoded,
      onPrimaryPressed: allConsented
          ? () => _handleEnableTracking(context)
          : null,
      secondaryClickableText: '',
      animateEllipse: false,
      ellipseSizeFactor: 0,
      ellipseIntensity: 0,
    );
  }

  Widget _buildConsentList() {
    final benefits = [
      'Durch dein persönliches Tracking können wir dich benachrichtigen, wenn in deiner Nähe jemand Unterstützung braucht – so kannst du schnell helfen.'
          .hardcoded,
      'Deine Daten bleiben sicher. Wir verkaufen deine Daten nicht und geben sie nicht weiter. Wir engagieren uns ethisch, um mit dir die Welt sicherer zu machen.'
          .hardcoded,
    ];

    return ListView.builder(
      itemCount: benefits.length,
      itemBuilder: (context, index) {
        return Padding(
          padding: const EdgeInsets.only(bottom: 24),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              GestureDetector(
                onTap: () {
                  setState(() {
                    _consents[index] = !_consents[index];
                  });
                },
                child: Container(
                  width: 20,
                  height: 20,
                  margin: const EdgeInsets.only(top: 2),
                  decoration: BoxDecoration(
                    color: _consents[index]
                        ? AppColors.primary
                        : Colors.transparent,
                    border: Border.all(
                      color: _consents[index]
                          ? AppColors.primary
                          : AppColors.border,
                      width: 2,
                    ),
                    borderRadius: BorderRadius.circular(4),
                  ),
                  child: _consents[index]
                      ? const Icon(
                          Icons.check,
                          color: AppColors.background,
                          size: 14,
                        )
                      : null,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: TextWidget(
                  benefits[index],
                  style: AppTextStyles.bodyMedium.copyWith(
                    color: AppColors.onBackground,
                    height: 1.5,
                  ),
                  maxLines: 5,
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  void _handleEnableTracking(BuildContext context) {
    context.push(RouteNames.setupAvailableTimes);
  }
}
