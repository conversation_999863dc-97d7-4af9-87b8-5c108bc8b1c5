import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:go_router/go_router.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:safea/core/router/route_names.dart';
import 'package:safea/core/theme/app_text_styles.dart';
import 'package:safea/core/theme/colors.dart';
import 'package:safea/core/theme/responsive_extensions.dart';
import 'package:safea/shared/extensions/string_hardcoded.dart';
import 'package:safea/shared/widgets/app_layout_template.dart';
import 'package:safea/shared/widgets/text_widget.dart';

/// Available times setup screen with time picker
class SetupAvailableTimesScreen extends HookConsumerWidget {
  const SetupAvailableTimesScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final selectedDay = useState('Montag');
    final startTime = useState(const TimeOfDay(hour: 0, minute: 0));
    final endTime = useState(const TimeOfDay(hour: 0, minute: 0));

    final weekdays = [
      'Sonntag'.hardcoded,
      'Montag'.hardcoded,
      'Dienstag'.hardcoded,
      'Mittwoch'.hardcoded,
      'Donnerstag'.hardcoded,
      'Freitag'.hardcoded,
      'Samstag'.hardcoded,
    ];

    void handleSetTime() {
      context.push(RouteNames.setupDone);
    }

    void handleNoTime() {
      startTime.value = const TimeOfDay(hour: 0, minute: 0);
      endTime.value = const TimeOfDay(hour: 0, minute: 0);
      context.push(RouteNames.setupDone);
    }

    return AppLayoutTemplate(
      showBackButton: true,
      title: 'Deine Verfügbarkeit'.hardcoded,
      titleTextStyle: AppTextStyles.authTabs.copyWith(
        color: AppColors.onBackground,
      ),
      description: 'Fülle alle Tage mit Zeiten aus, \num dein Setup zu beenden.'
          .hardcoded,
      content: Column(
        children: [
          SizedBox(height: context.responsiveSpacing(16)),
          _buildDaySelector(
            context,
            weekdays,
            selectedDay.value,
            (day) => selectedDay.value = day,
          ),
          SizedBox(height: context.responsiveSpacing(20)),
          Expanded(child: _buildTimeSelector(context, startTime, endTime)),
        ],
      ),
      primaryButtonText: 'Zeit setzen'.hardcoded,
      onPrimaryPressed: handleSetTime,
      secondaryClickableText: 'Keine Zeit'.hardcoded,
      onSecondaryTextTap: handleNoTime,
      animateEllipse: false,
      ellipseSizeFactor: 0,
      ellipseIntensity: 0,
    );
  }

  Widget _buildDaySelector(
    BuildContext context,
    List<String> weekdays,
    String selectedDay,
    ValueChanged<String> onSelected,
  ) {
    final selectedIndex = weekdays.indexOf(selectedDay);

    return Align(
      alignment: Alignment.centerLeft,
      child: GestureDetector(
        onHorizontalDragEnd: (details) {
          if (details.primaryVelocity! < 0 &&
              selectedIndex < weekdays.length - 1) {
            onSelected(weekdays[selectedIndex + 1]);
          } else if (details.primaryVelocity! > 0 && selectedIndex > 0) {
            onSelected(weekdays[selectedIndex - 1]);
          }
        },
        child: SizedBox(
          width: context.widthPercent(0.5),
          height: 40,
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              if (selectedIndex > 0)
                _buildFadedDay(
                  text: weekdays[selectedIndex - 1],
                  gradient: const LinearGradient(
                    colors: [Colors.white, Colors.transparent],
                  ),
                  onTap: () => onSelected(weekdays[selectedIndex - 1]),
                )
              else
                const SizedBox(width: 40),

              Expanded(
                child: Center(
                  child: AnimatedSwitcher(
                    duration: const Duration(milliseconds: 30),
                    transitionBuilder: (child, animation) {
                      return FadeTransition(
                        opacity: animation,
                        child: SlideTransition(
                          position: Tween<Offset>(
                            begin: const Offset(0.2, 0),
                            end: Offset.zero,
                          ).animate(animation),
                          child: child,
                        ),
                      );
                    },
                    child: TextWidget(
                      weekdays[selectedIndex],
                      key: ValueKey(weekdays[selectedIndex]),
                      style: const TextStyle(
                        fontFamily: 'Sana Sans VAR',
                        fontWeight: FontWeight.w400,
                        fontSize: 17,
                        height: 19.6 / 17,
                        color: Colors.white,
                      ),
                    ),
                  ),
                ),
              ),

              if (selectedIndex < weekdays.length - 1)
                _buildFadedDay(
                  text: weekdays[selectedIndex + 1],
                  gradient: const LinearGradient(
                    begin: Alignment.centerRight,
                    end: Alignment.centerLeft,
                    colors: [Colors.white, Colors.transparent],
                  ),
                  onTap: () => onSelected(weekdays[selectedIndex + 1]),
                )
              else
                const SizedBox(width: 40),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildFadedDay({
    required String text,
    required Gradient gradient,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: ShaderMask(
        shaderCallback: (bounds) => gradient.createShader(bounds),
        blendMode: BlendMode.srcIn,
        child: TextWidget(
          text,
          style: const TextStyle(
            fontFamily: 'Sana Sans VAR',
            fontWeight: FontWeight.w400,
            fontSize: 11,
            height: 19.6 / 11,
            color: Colors.white,
          ),
        ),
      ),
    );
  }

  Widget _buildTimeSelector(
    BuildContext context,
    ValueNotifier<TimeOfDay> startTime,
    ValueNotifier<TimeOfDay> endTime,
  ) {
    return Column(
      children: [
        SizedBox(height: context.responsiveSpacing(12)),
        _buildWheelTimePicker(context, startTime, 'von'),
        SizedBox(height: context.responsiveSpacing(12)),
        TextWidget(
          'bis'.hardcoded,
          style: AppTextStyles.bodyMedium.copyWith(
            color: AppColors.onBackgroundSecondary,
          ),
        ),
        SizedBox(height: context.responsiveSpacing(12)),
        _buildWheelTimePicker(context, endTime, 'bis'),
        const Spacer(),
        _buildTimeIndicators(),
        SizedBox(height: context.responsiveSpacing(16)),
      ],
    );
  }

  Widget _buildWheelTimePicker(
    BuildContext context,
    ValueNotifier<TimeOfDay> timeNotifier,
    String label,
  ) {
    final pickerHeight = context.isMobile ? 160.0 : 200.0;
    return Container(
      padding: EdgeInsets.symmetric(
        horizontal: context.responsiveSpacing(24),
        vertical: context.responsiveSpacing(8),
      ),
      height: pickerHeight,
      child: Stack(
        alignment: Alignment.center,
        children: [
          // Circular glow background for the entire time display
          Container(
            width: context.isMobile ? 160 : 200,
            height: context.isMobile ? 120 : 150,
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              boxShadow: [
                BoxShadow(
                  color: const Color(0xFF00FF94).withValues(alpha: 0.3),
                  blurRadius: context.isMobile ? 40 : 60,
                  spreadRadius: context.isMobile ? 15 : 20,
                ),
                BoxShadow(
                  color: const Color(0xFF00FF94).withValues(alpha: 0.2),
                  blurRadius: context.isMobile ? 70 : 100,
                  spreadRadius: context.isMobile ? 30 : 40,
                ),
                BoxShadow(
                  color: const Color(0xFF00FF94).withValues(alpha: 0.1),
                  blurRadius: context.isMobile ? 100 : 150,
                  spreadRadius: context.isMobile ? 40 : 60,
                ),
              ],
            ),
          ),

          // Time display content
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              _buildWheelNumber(
                value: timeNotifier.value.hour,
                onChanged: (newHour) {
                  timeNotifier.value = TimeOfDay(
                    hour: newHour,
                    minute: timeNotifier.value.minute,
                  );
                },
                maxValue: 23,
              ),
              Container(
                margin: const EdgeInsets.symmetric(horizontal: 8),
                child: TextWidget(
                  ':',
                  style: AppTextStyles.responsiveTimeLarge(
                    context,
                  ).copyWith(color: const Color(0xFFFFFFFF)),
                ),
              ),
              _buildWheelNumber(
                value: timeNotifier.value.minute,
                onChanged: (newMinute) {
                  timeNotifier.value = TimeOfDay(
                    hour: timeNotifier.value.hour,
                    minute: newMinute,
                  );
                },
                maxValue: 59,
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildWheelNumber({
    required int value,
    required void Function(int) onChanged,
    required int maxValue,
  }) {
    return GestureDetector(
      onPanUpdate: (details) {
        if (details.delta.dy.abs() > 2) {
          if (details.delta.dy > 0) {
            final newValue = value - 1;
            onChanged(newValue < 0 ? maxValue : newValue);
          } else {
            final newValue = value + 1;
            onChanged(newValue > maxValue ? 0 : newValue);
          }
        }
      },
      child: SizedBox(
        width: 80,
        height: 140,
        child: Stack(
          alignment: Alignment.center,
          children: [
            // Second previous number (top most, very faded)
            Positioned(
              top: 10,
              child: Transform.translate(
                offset: const Offset(12, 0),
                child: Opacity(
                  opacity: 0.2,
                  child: ShaderMask(
                    shaderCallback: (bounds) => const LinearGradient(
                      stops: [0.0, 0.8659],
                      colors: [Color(0xFFFFFFFF), Color(0xFF151515)],
                    ).createShader(bounds),
                    blendMode: BlendMode.srcIn,
                    child: TextWidget(
                      _getPreviousValue(
                        _getPreviousValue(value, maxValue),
                        maxValue,
                      ).toString().padLeft(2, '0'),
                      style: const TextStyle(
                        fontFamily: 'Sana Sans VAR',
                        fontWeight: FontWeight.w400,
                        fontSize: 11,
                        height: 19.6 / 11,
                        color: Colors.white,
                      ),
                    ),
                  ),
                ),
              ),
            ),

            // Previous number (upper, faded)
            Positioned(
              top: 40,
              child: Transform.translate(
                offset: const Offset(6, 0),
                child: Opacity(
                  opacity: 0.2,
                  child: ShaderMask(
                    shaderCallback: (bounds) => const LinearGradient(
                      stops: [0.0, 0.8659],
                      colors: [Color(0xFFFFFFFF), Color(0xFF151515)],
                    ).createShader(bounds),
                    blendMode: BlendMode.srcIn,
                    child: TextWidget(
                      _getPreviousValue(
                        value,
                        maxValue,
                      ).toString().padLeft(2, '0'),
                      style: const TextStyle(
                        fontFamily: 'Sana Sans VAR',
                        fontWeight: FontWeight.w400,
                        fontSize: 11,
                        height: 19.6 / 11,
                        letterSpacing: 0,
                        color: Colors.white,
                      ),
                    ),
                  ),
                ),
              ),
            ),

            // Current number (center, full opacity)
            Center(
              child: AnimatedSwitcher(
                duration: const Duration(milliseconds: 150),
                transitionBuilder: (child, animation) {
                  return SlideTransition(
                    position: Tween<Offset>(
                      begin: const Offset(0, 0.3),
                      end: Offset.zero,
                    ).animate(animation),
                    child: FadeTransition(opacity: animation, child: child),
                  );
                },
                child: Builder(
                  builder: (context) => TextWidget(
                    value.toString().padLeft(2, '0'),
                    key: ValueKey(value),
                    style: AppTextStyles.responsiveTimeLarge(
                      context,
                    ).copyWith(color: const Color(0xFFFFFFFF)),
                  ),
                ),
              ),
            ),

            // Next number (lower, faded)
            Positioned(
              bottom: 40,
              child: Transform.translate(
                offset: const Offset(6, 0),
                child: Opacity(
                  opacity: 0.2,
                  child: ShaderMask(
                    shaderCallback: (bounds) => const LinearGradient(
                      stops: [0.0, 0.8659],
                      colors: [Color(0xFFFFFFFF), Color(0xFF151515)],
                    ).createShader(bounds),
                    blendMode: BlendMode.srcIn,
                    child: TextWidget(
                      _getNextValue(value, maxValue).toString().padLeft(2, '0'),
                      style: const TextStyle(
                        fontFamily: 'Sana Sans VAR',
                        fontWeight: FontWeight.w400,
                        fontSize: 11,
                        height: 19.6 / 11,
                        letterSpacing: 0,
                        color: Colors.white,
                      ),
                    ),
                  ),
                ),
              ),
            ),

            // Second next number (bottom most, very faded)
            Positioned(
              bottom: 10,
              child: Transform.translate(
                offset: const Offset(12, 0),
                child: Opacity(
                  opacity: 0.2,
                  child: ShaderMask(
                    shaderCallback: (bounds) => const LinearGradient(
                      stops: [0.0, 0.8659],
                      colors: [Color(0xFFFFFFFF), Color(0xFF151515)],
                    ).createShader(bounds),
                    blendMode: BlendMode.srcIn,
                    child: TextWidget(
                      _getNextValue(
                        _getNextValue(value, maxValue),
                        maxValue,
                      ).toString().padLeft(2, '0'),
                      style: const TextStyle(
                        fontFamily: 'Sana Sans VAR',
                        fontWeight: FontWeight.w400,
                        fontSize: 11,
                        height: 19.6 / 11,
                        letterSpacing: 0,
                        color: Colors.white,
                      ),
                    ),
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  int _getPreviousValue(int current, int maxValue) {
    return current == 0 ? maxValue : current - 1;
  }

  int _getNextValue(int current, int maxValue) {
    return current == maxValue ? 0 : current + 1;
  }

  Widget _buildTimeIndicators() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [_circleDot(), const SizedBox(width: 8), _circleDot()],
    );
  }

  Widget _circleDot() {
    return Container(
      width: 8,
      height: 8,
      decoration: const BoxDecoration(
        color: AppColors.success,
        shape: BoxShape.circle,
      ),
    );
  }
}
