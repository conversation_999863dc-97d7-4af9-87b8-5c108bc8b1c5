import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:safea/core/router/route_names.dart';
import 'package:safea/core/theme/app_text_styles.dart';
import 'package:safea/core/theme/colors.dart';
import 'package:safea/core/storage/storage_providers.dart';
import 'package:safea/shared/extensions/string_hardcoded.dart';
import 'package:safea/shared/widgets/app_layout_template.dart';
import 'package:safea/shared/widgets/text_widget.dart';

/// Setup completion screen
class SetupDoneScreen extends ConsumerWidget {
  const SetupDoneScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return AppLayoutTemplate(
      showTitle: false,
      content: Column(
        children: [
          const Spacer(),
          _buildSuccessIcon(),
          const SizedBox(height: 40),
          _buildTitle(),
          const SizedBox(height: 16),
          _buildDescription(),
          const Spacer(),
        ],
      ),
      primaryButtonText: 'Zur App'.hardcoded,
      onPrimaryPressed: () => _handleComplete(context, ref),
      animateEllipse: false,
    );
  }

  Widget _buildSuccessIcon() {
    return Container(
      width: 120,
      height: 120,
      decoration: const BoxDecoration(
        color: AppColors.success,
        shape: BoxShape.circle,
      ),
      child: const Icon(Icons.check, color: Colors.white, size: 60),
    );
  }

  Widget _buildTitle() {
    return TextWidget(
      'Setup abgeschlossen!'.hardcoded,
      style: AppTextStyles.tagline.copyWith(fontSize: 28),
      textAlign: TextAlign.center,
    );
  }

  Widget _buildDescription() {
    return TextWidget(
      'Dein Konto ist jetzt vollständig eingerichtet und bereit für die Nutzung. Du kannst jetzt anderen Menschen in Notfällen helfen.'
          .hardcoded,
      style: AppTextStyles.bodyMedium.copyWith(
        color: AppColors.onBackgroundSecondary,
      ),
      textAlign: TextAlign.center,
    );
  }

  Future<void> _handleComplete(BuildContext context, WidgetRef ref) async {
    // Mark setup as completed
    await ref.read(setupStatusProvider.notifier).setCompleted(true);

    // Navigate to dashboard or main app
    if (context.mounted) {
      context.go(RouteNames.dashboardHome);
    }
  }
}
