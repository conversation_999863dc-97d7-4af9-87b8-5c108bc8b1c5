import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:safea/core/theme/app_text_styles.dart';
import 'package:safea/core/theme/colors.dart';
import 'package:safea/shared/extensions/string_hardcoded.dart';
import 'package:safea/shared/widgets/app_layout_template.dart';
import 'package:safea/shared/widgets/text_widget.dart';

/// AGB (Terms & Conditions) screen with scrollable content
class Setup01AgbScreen extends StatelessWidget {
  const Setup01AgbScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return AppLayoutTemplate(
      showBackButton: true,
      title: "Unsere AGB's".hardcoded,
      titleTextStyle: AppTextStyles.authTabs.copyWith(
        color: AppColors.onBackground,
      ),
      content: _buildAgbContent(),
      primaryButtonText: "Ich habe die AGB's gelesen und stimme zu".hardcoded,
      onPrimaryPressed: () => context.pop(),
      animateEllipse: false,
      ellipseSizeFactor: 0,
      ellipseIntensity: 0,
    );
  }

  Widget _buildAgbContent() {
    return const SingleChildScrollView(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          TextWidget(
            'The European languages are members of the same family. Their separate existence is a myth. For science, music, sport, etc, Europe uses the same vocabulary. The languages only differ in their grammar, their pronunciation and their most common words. Everyone realizes why a new common language would be desirable: one could refuse to pay expensive translators. To achieve this, it would be necessary to have uniform grammar, pronunciation and more common words. If several languages coalesce, the grammar of the resulting language is more simple and regular The European languages are members of the same family. Their separate existence is a myth. For science, music, sport, etc, Europe uses the same vocabulary. The languages only differ in their grammar, their pronunciation and their most common words. Everyone realizes why a new common language would be desirable: one could refuse to pay expensive translators. To achieve this, it would be necessary to have uniform grammar, pronunciation and more common words. If several languages coalesce, the grammar of the resulting language is more simple and regular The European languages are members of the same family. Their separate existence is a myth. For science, music, sport, etc, Europe uses the same vocabulary. The languages only differ in their grammar, their pronunciation and their most common words. Everyone realizes why a new common language would be desirable: one could refuse to pay expensive translators. To achieve this, it would be necessary to have uniform grammar, pronunciation and more common words. If several languages coalesce, the grammar of the resulting language is more simple and regular than that of the individual languages. The new common language will be more simple and regular than the existing European languages. It will be as simple as Occidental; in fact, it will be Occidental. To an English person, it will seem like simplified English, as a skeptical Cambridge friend of mine told me what Occidental is.The European languages are members of the same family. Their separate existence is a myth. For science, music, sport, etc, Europe uses the same vocabulary. Th',
            style: AppTextStyles.small,
            maxLines: 50,
          ),
        ],
      ),
    );
  }
}
