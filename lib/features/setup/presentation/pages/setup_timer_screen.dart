import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:safea/core/router/route_names.dart';
import 'package:safea/core/storage/storage_providers.dart';
import 'package:safea/core/theme/app_text_styles.dart';
import 'package:safea/shared/widgets/app_button.dart';

/// Setup timer screen with green gradient and countdown
class SetupTimerScreen extends ConsumerStatefulWidget {
  const SetupTimerScreen({super.key});

  @override
  ConsumerState<SetupTimerScreen> createState() => _SetupTimerScreenState();
}

class _SetupTimerScreenState extends ConsumerState<SetupTimerScreen>
    with TickerProviderStateMixin {
  late AnimationController _timerController;
  late Animation<double> _timerAnimation;
  late AnimationController _pulseController;
  late Animation<double> _pulseAnimation;

  int _remainingSeconds = 120;
  bool _isTimerStarted = false;

  @override
  void initState() {
    super.initState();
    _setupAnimations();
  }

  void _setupAnimations() {
    // Timer animation
    _timerController = AnimationController(
      duration: const Duration(seconds: 120),
      vsync: this,
    );

    _timerAnimation = Tween<double>(
      begin: 1.0,
      end: 0.0,
    ).animate(CurvedAnimation(parent: _timerController, curve: Curves.linear));

    _timerController.addListener(() {
      setState(() {
        _remainingSeconds = (120 * (1 - _timerController.value)).round();
      });
    });

    _timerController.addStatusListener((status) {
      if (status == AnimationStatus.completed) {
        _handleTimerComplete();
      }
    });

    // Pulse animation for the circle
    _pulseController = AnimationController(
      duration: const Duration(seconds: 2),
      vsync: this,
    );

    _pulseAnimation = Tween<double>(begin: 1.0, end: 1.1).animate(
      CurvedAnimation(parent: _pulseController, curve: Curves.easeInOut),
    );

    _pulseController.repeat(reverse: true);
  }

  @override
  void dispose() {
    _timerController.dispose();
    _pulseController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Container(
        decoration: const BoxDecoration(
          gradient: RadialGradient(
            center: Alignment.center,
            radius: 1.2,
            colors: [
              Color(0xFF00FF88), // Bright green center
              Color(0xFF00CC66), // Medium green
              Color(0xFF009944), // Darker green
              Color(0xFF001122), // Very dark background
            ],
            stops: [0.0, 0.3, 0.6, 1.0],
          ),
        ),
        child: SafeArea(
          child: Padding(
            padding: const EdgeInsets.all(24),
            child: Column(
              children: [
                const SizedBox(height: 60),
                _buildTitle(),
                const Spacer(),
                _buildTimerCircle(),
                const Spacer(),
                _buildStartButton(),
                const SizedBox(height: 40),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildTitle() {
    return Column(
      children: [
        Text(
          'Werde in',
          style: AppTextStyles.tagline.copyWith(
            fontSize: 32,
            color: Colors.white,
            fontWeight: FontWeight.w300,
          ),
        ),
        Text(
          '$_remainingSeconds Sekunden',
          style: AppTextStyles.tagline.copyWith(
            fontSize: 32,
            color: Colors.white,
            fontWeight: FontWeight.w300,
          ),
        ),
        Text(
          'zur Unterstützung.',
          style: AppTextStyles.tagline.copyWith(
            fontSize: 32,
            color: Colors.white,
            fontWeight: FontWeight.w300,
          ),
        ),
      ],
    );
  }

  Widget _buildTimerCircle() {
    return AnimatedBuilder(
      animation: _pulseAnimation,
      builder: (context, child) {
        return Transform.scale(
          scale: _pulseAnimation.value,
          child: Container(
            width: 300,
            height: 300,
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              color: Colors.black.withOpacity(0.3),
              border: Border.all(
                color: Colors.white.withOpacity(0.2),
                width: 2,
              ),
            ),
            child: Center(
              child: Text(
                'safea',
                style: AppTextStyles.tagline.copyWith(
                  fontSize: 24,
                  color: Colors.white,
                  fontWeight: FontWeight.w300,
                ),
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildStartButton() {
    return Container(
      width: double.infinity,
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(25),
      ),
      child: AppButton(
        text: _isTimerStarted ? 'Timer läuft...' : 'Setup starten und loslegen',
        onPressed: _isTimerStarted ? null : _handleStartSetup,
        type: AppButtonType.primary,
        size: AppButtonSize.large,
        isEnabled: !_isTimerStarted,
      ),
    );
  }

  void _handleStartSetup() {
    setState(() {
      _isTimerStarted = true;
    });
    _timerController.forward();
  }

  void _handleTimerComplete() async {
    // Mark timer setup as completed
    await ref.read(timerSetupStatusProvider.notifier).setCompleted(true);

    // Navigate to permissions or next setup step
    if (mounted) {
      context.go(RouteNames.setupTracking);
    }
  }
}
