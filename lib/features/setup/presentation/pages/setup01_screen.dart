import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:safea/core/router/route_names.dart';
import 'package:safea/core/storage/storage_providers.dart';
import 'package:safea/core/theme/app_text_styles.dart';
import 'package:safea/core/theme/colors.dart';
import 'package:safea/shared/extensions/string_hardcoded.dart';
import 'package:safea/shared/widgets/app_layout_template.dart';
import 'package:safea/shared/widgets/text_widget.dart';

/// Setup01 screen with agreement points
class Setup01Screen extends ConsumerStatefulWidget {
  const Setup01Screen({super.key});

  @override
  ConsumerState<Setup01Screen> createState() => _Setup01ScreenState();
}

class _Setup01ScreenState extends ConsumerState<Setup01Screen> {
  final List<bool> _agreements = List.filled(5, false);

  @override
  Widget build(BuildContext context) {
    final allAgreed = _agreements.every((agreed) => agreed);

    return AppLayoutTemplate(
      showBackButton: true,
      title: 'Unsere Vereinbarung'.hardcoded,
      titleTextStyle: AppTextStyles.authTabs.copyWith(
        color: AppColors.onBackground,
      ),
      content: _buildAgreementList(),
      primaryButtonText: 'Bestätige unsere Vereinbarung'.hardcoded,
      onPrimaryPressed: allAgreed ? _handleConfirm : null,
      secondaryClickableText: 'Unsere AGB\'s'.hardcoded,
      onSecondaryTextTap: () => context.push(RouteNames.setup01Agb),
      animateEllipse: false,
      ellipseSizeFactor: 0,
      ellipseIntensity: 0,
    );
  }

  Widget _buildAgreementList() {
    final agreements = [
      'Jede Minute zählt!\nMit deiner Zeit leistest du echte, greifbare Unterstützung für Menschen in schwierigen Situationen, die dringend Hilfe brauchen.'
          .hardcoded,
      'Sei nur dann zur Hilfe bereit, wenn du dich selbst sicher fühlst. So schützt du dich und andere effektiv.'
          .hardcoded,
      'Wir bringen Hilfesuchende und Helfer zusammen, bieten aber keinen Polizeischutz oder Sicherheitsdienstleistungen an.'
          .hardcoded,
      'Empathie und Respekt machen dich zur starken Stütze – sei die Schulter, auf die Menschen in Not vertrauen können.'
          .hardcoded,
      'Gewalt bringt keine Lösung. Informiere bei ernsten Vorfällen Polizei oder Rettungsdienst vor Ort. Bleibe selbst ruhig und friedvoll.'
          .hardcoded,
    ];

    return ListView.builder(
      itemCount: agreements.length,
      itemBuilder: (context, index) {
        return Padding(
          padding: const EdgeInsets.only(bottom: 24),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              GestureDetector(
                onTap: () {
                  setState(() {
                    _agreements[index] = !_agreements[index];
                  });
                },
                child: Container(
                  width: 20,
                  height: 20,
                  margin: const EdgeInsets.only(top: 2),
                  decoration: BoxDecoration(
                    color: _agreements[index]
                        ? AppColors.primary
                        : Colors.transparent,
                    border: Border.all(
                      color: _agreements[index]
                          ? AppColors.primary
                          : AppColors.border,
                      width: 2,
                    ),
                    borderRadius: BorderRadius.circular(4),
                  ),
                  child: _agreements[index]
                      ? const Icon(
                          Icons.check,
                          color: AppColors.background,
                          size: 14,
                        )
                      : null,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: TextWidget(
                  agreements[index],
                  style: AppTextStyles.bodyMedium.copyWith(
                    color: AppColors.onBackground,
                    height: 1.5,
                  ),
                  maxLines: 5,
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  Future<void> _handleConfirm() async {
    // Mark agreements as accepted
    await ref.read(agreementsStatusProvider.notifier).setAccepted(true);

    // Navigate directly to setup start screen (120 seconds page)
    if (mounted) {
      await context.push(RouteNames.setupStart);
    }
  }
}
