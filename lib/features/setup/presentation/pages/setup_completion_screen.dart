import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:safea/core/router/route_names.dart';
import 'package:safea/core/storage/storage_providers.dart';
import 'package:safea/core/theme/app_text_styles.dart';
import 'package:safea/core/theme/colors.dart';
import 'package:safea/shared/extensions/string_hardcoded.dart';
import 'package:safea/shared/widgets/app_layout_template.dart';

/// Setup completion screen with green gradient design
class SetupCompletionScreen extends HookConsumerWidget {
  const SetupCompletionScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return AppLayoutTemplate(
      content: const SizedBox.shrink(),
      title: 'Super!\nDein setup\nist komplett.'.hardcoded,
      titleTextStyle: AppTextStyles.setupTabs.copyWith(
        color: AppColors.onPrimary,
      ),
      primaryButtonText: 'Jetzt als Privatperson anmelden'.hardcoded,
      onPrimaryPressed: () => _handleComplete(context, ref),
      secondaryClickableText: '',
      showEllipseLogo: true,
      ellipseLogoText: 'safea',
      ellipseLogoStyle: AppTextStyles.appLogoText,
      ellipseIntensity: 5,
    );
  }

  Future<void> _handleComplete(BuildContext context, WidgetRef ref) async {
    // Mark all setup steps as completed
    await Future.wait([
      ref.read(setupStatusProvider.notifier).setCompleted(true),
      ref.read(permissionsStatusProvider.notifier).setGranted(true),
      ref.read(agreementsStatusProvider.notifier).setAccepted(true),
      ref.read(timerSetupStatusProvider.notifier).setCompleted(true),
    ]);

    // Navigate to dashboard home
    if (context.mounted) {
      context.go(RouteNames.dashboardHome);
    }
  }
}
