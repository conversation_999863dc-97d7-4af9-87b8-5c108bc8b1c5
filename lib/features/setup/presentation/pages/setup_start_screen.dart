import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:safea/core/router/route_names.dart';
import 'package:safea/core/theme/app_text_styles.dart';
import 'package:safea/core/theme/colors.dart';
import 'package:safea/shared/extensions/string_hardcoded.dart';
import 'package:safea/shared/widgets/app_layout_template.dart';

/// Start setup screen with 120 seconds design (no counter)
class SetupStartScreen extends ConsumerWidget {
  const SetupStartScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return AppLayoutTemplate(
      showEllipseLogo: true,
      title: 'Werde in \n120 Sekunden \nzur Unterstützung.'.hardcoded,
      titleTextStyle: AppTextStyles.authTabs.copyWith(
        color: AppColors.onBackground,
      ),
      primaryButtonText: 'Setup starten und loslegen'.hardcoded,
      onPrimaryPressed: () => _handleStartSetup(context),
      secondaryClickableText: '',
      ellipseLogoText: 'safea',
      ellipseLogoStyle: AppTextStyles.appLogoText,
    );
  }

  void _handleStartSetup(BuildContext context) {
    // Navigate to tracking setup
    context.push(RouteNames.setupTracking);
  }
}
