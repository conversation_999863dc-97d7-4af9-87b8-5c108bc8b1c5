import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:safea/core/theme/app_text_styles.dart';
import 'package:safea/core/theme/colors.dart';
import 'package:safea/core/theme/responsive_extensions.dart';
import 'package:safea/shared/widgets/text_widget.dart';
import 'package:safea/shared/widgets/wavy_ellipse_effect.dart';

/// Standardized layout template for all app screens with ellipse background
///
/// This template provides a consistent layout structure with:
/// 1. Ellipse background effect (customizable color)
/// 2. Title (positioned at top-left) with optional back button
/// 3. Content (centered, varies by screen)
/// 4. Description (bottom section)
/// 5. Actions row (below description, contains button(s) and optional clickable text)
class AppLayoutTemplate extends StatelessWidget {
  const AppLayoutTemplate({
    super.key,
    this.title,
    this.content,
    this.description,
    this.primaryButtonText,
    this.onPrimaryPressed,
    this.secondaryClickableText,
    this.onSecondaryTextTap,
    this.showTitle = true,
    this.showActions = true,
    this.showBackButton = false,
    this.onBackPressed,
    this.glowColor = AppColors.ellipseGreen,
    this.showEllipseLogo = false,
    this.ellipseLogoText,
    this.ellipseLogoStyle,
    this.animateEllipse = true,
    this.ellipseIntensity = 1.0,
    this.titleTextStyle,
    this.ellipseSizeFactor = 1.0,
    this.profileImage,
    this.showProfileImage = false,
  }) : assert(
         !showTitle || title != null,
         'If showTitle is true, title must not be null',
       );

  /// Title string (required)
  final String? title;

  /// Whether to show the title
  final bool showTitle;

  /// Whether to show the actions row (primary button and secondary text)
  final bool showActions;

  /// Whether to show the back button
  final bool showBackButton;

  /// Callback for back button press (if null, uses context.pop())
  final VoidCallback? onBackPressed;

  /// Main content widget (centered)
  final Widget? content;

  /// Description text (bottom section)
  final String? description;

  /// Primary button text
  final String? primaryButtonText;

  /// Callback for primary button press
  final VoidCallback? onPrimaryPressed;

  /// Secondary clickable text (right-aligned in actions row)
  final String? secondaryClickableText;

  /// Callback for secondary clickable text tap
  final VoidCallback? onSecondaryTextTap;

  /// Ellipse glow color (green for most screens, red for dashboard user screens)
  final Color glowColor;

  /// Whether to show logo in the ellipse background
  final bool showEllipseLogo;

  /// Logo text for ellipse background
  final String? ellipseLogoText;

  /// Logo style for ellipse background
  final TextStyle? ellipseLogoStyle;

  /// Whether to animate the ellipse effect
  final bool animateEllipse;

  /// Intensity of the ellipse effect
  final double ellipseIntensity;

  /// Custom text style for title (optional)
  final TextStyle? titleTextStyle;

  /// Scale factor for ellipse dimensions (optional)
  final double ellipseSizeFactor;

  /// Profile image path for ellipse background
  final String? profileImage;

  /// Whether to show profile image in ellipse background
  final bool showProfileImage;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.background,
      body: Stack(
        children: [
          // Ellipse background effect
          Center(
            child: WavyEllipseEffect(
              useDefaultDimensions: true,
              intensity: ellipseIntensity,
              showLogo: showEllipseLogo,
              logoText: ellipseLogoText,
              logoStyle: ellipseLogoStyle,
              animate: animateEllipse,
              glowColor: glowColor,
              sizeFactor: ellipseSizeFactor,
              profileImage: profileImage,
              showProfileImage: showProfileImage,
            ),
          ),
          // Content overlay
          SafeArea(
            child: Padding(
              padding: context.screenPaddingHorizontal,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Title section with optional back button (top-left)
                  if (showTitle) ...[
                    SizedBox(height: context.spacingXl),
                    _buildTitleWithBackButton(context),
                    SizedBox(height: context.spacingXl),
                  ],

                  // Content section (centered, expandable)
                  Expanded(child: content ?? const SizedBox.shrink()),

                  // Description section (bottom)
                  if (description != null) ...[
                    _buildDescription(context),
                    SizedBox(height: context.spacingLg),
                  ],

                  // Actions section (bottom)
                  if (showActions &&
                      (primaryButtonText != null ||
                          secondaryClickableText != null)) ...[
                    _buildActions(context),
                    SizedBox(height: context.spacingXl),
                  ],
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTitleWithBackButton(BuildContext context) {
    if (!showBackButton) {
      return _buildTitle(context);
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Back button - using GestureDetector for precise alignment
        GestureDetector(
          onTap: onBackPressed ?? () => context.pop(),
          child: const Icon(
            Icons.arrow_back_sharp,
            color: AppColors.onBackground,
            size: 24,
          ),
        ),
        SizedBox(height: context.spacingXs),
        // Title
        _buildTitle(context),
      ],
    );
  }

  Widget _buildTitle(BuildContext context) {
    final displayText = title ?? '';

    return TextWidget(
      displayText,
      style: titleTextStyle ?? AppTextStyles.authTitle(context),
    );
  }

  Widget _buildDescription(BuildContext context) {
    return TextWidget(
      description!,
      style: AppTextStyles.authDescription(context),
    );
  }

  Widget _buildActions(BuildContext context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        if (primaryButtonText != null)
          Expanded(
            flex: 2,
            child: AppButton(
              text: primaryButtonText!,
              onPressed: onPrimaryPressed,
            ),
          ),
        if (primaryButtonText != null && secondaryClickableText != null)
          SizedBox(width: context.spacingXs),
        if (secondaryClickableText != null)
          Expanded(
            child: GestureDetector(
              onTap: onSecondaryTextTap,
              child: TextWidget(
                secondaryClickableText!,
                style: AppTextStyles.authClickableText(context),
                textAlign: TextAlign.end,
              ),
            ),
          ),
      ],
    );
  }
}

/// Standardized button for app screens
class AppButton extends StatelessWidget {
  const AppButton({
    required this.text,
    required this.onPressed,
    this.isLoading = false,
    super.key,
  });

  final String text;
  final VoidCallback? onPressed;
  final bool isLoading;

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      height: context.responsiveSpacing(48), // Responsive button height
      child: ElevatedButton(
        onPressed: isLoading ? null : onPressed,
        style: ElevatedButton.styleFrom(
          backgroundColor: const Color(0xFFFFFFFF), // White background
          foregroundColor: const Color(0xFF1F1F1F), // Dark text
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(24), // 24px border radius
          ),
          padding: EdgeInsets.symmetric(
            horizontal: context.responsiveSpacing(12),
            vertical: context.responsiveSpacing(6),
          ),
          elevation: 0,
        ),
        child: isLoading
            ? const SizedBox(
                width: 20,
                height: 20,
                child: CircularProgressIndicator(
                  strokeWidth: 2,
                  valueColor: AlwaysStoppedAnimation<Color>(Color(0xFF1F1F1F)),
                ),
              )
            : TextWidget(
                text,
                style: AppTextStyles.authButtonText(context),
                textAlign: TextAlign.center,
              ),
      ),
    );
  }
}

/// Extension for easy access to app layout patterns
extension AppLayoutExtensions on Widget {
  Widget wrapInAppLayout({
    required String title,
    String? description,
    String? primaryButtonText,
    VoidCallback? onPrimaryPressed,
    String? secondaryClickableText,
    VoidCallback? onSecondaryTextTap,
    bool showTitle = true,
    bool showActions = true,
    bool showBackButton = false,
    VoidCallback? onBackPressed,
    Color glowColor = const Color(0xFF00FF88),
    bool showEllipseLogo = false,
    String? ellipseLogoText,
    TextStyle? ellipseLogoStyle,
    bool animateEllipse = false,
    double ellipseIntensity = 1.0,
  }) {
    return AppLayoutTemplate(
      title: title,
      content: this,
      description: description,
      primaryButtonText: primaryButtonText,
      onPrimaryPressed: onPrimaryPressed,
      secondaryClickableText: secondaryClickableText,
      onSecondaryTextTap: onSecondaryTextTap,
      showTitle: showTitle,
      showActions: showActions,
      showBackButton: showBackButton,
      onBackPressed: onBackPressed,
      glowColor: glowColor,
      showEllipseLogo: showEllipseLogo,
      ellipseLogoText: ellipseLogoText,
      ellipseLogoStyle: ellipseLogoStyle,
      animateEllipse: animateEllipse,
      ellipseIntensity: ellipseIntensity,
    );
  }
}
