import 'package:flutter/material.dart';
import 'package:safea/shared/widgets/wavy_ellipse_effect.dart';

/// A layout template that provides the ellipse background for all screens
class EllipseBackgroundLayout extends StatelessWidget {
  final Widget child;
  final Color glowColor;
  final bool showLogo;
  final String? logoText;
  final TextStyle? logoStyle;
  final bool animate;
  final double intensity;

  const EllipseBackgroundLayout({
    super.key,
    required this.child,
    this.glowColor = const Color(0xFF00FF88), // Default green
    this.showLogo = false,
    this.logoText,
    this.logoStyle,
    this.animate = true,
    this.intensity = 1.0,
  });

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.black,
      body: Stack(
        children: [
          // Ellipse background effect
          Center(
            child: WavyEllipseEffect(
              useDefaultDimensions: true,
              intensity: intensity,
              showLogo: showLogo,
              logoText: logoText,
              logoStyle: logoStyle,
              animate: animate,
              glowColor: glowColor,
            ),
          ),
          // Content overlay
          child,
        ],
      ),
    );
  }
}

/// Extension to easily apply ellipse background to any widget
extension EllipseBackgroundExtension on Widget {
  Widget withEllipseBackground({
    Color glowColor = const Color(0xFF00FF88),
    bool showLogo = false,
    String? logoText,
    TextStyle? logoStyle,
    bool animate = true,
    double intensity = 1.0,
  }) {
    return EllipseBackgroundLayout(
      glowColor: glowColor,
      showLogo: showLogo,
      logoText: logoText,
      logoStyle: logoStyle,
      animate: animate,
      intensity: intensity,
      child: this,
    );
  }
}
