import 'package:flutter/material.dart';
import 'package:safea/core/theme/app_text_styles.dart';
import 'package:safea/core/theme/colors.dart';
import 'package:safea/core/theme/responsive_extensions.dart';

enum AppButtonType { primary, secondary, outline, text }

enum AppButtonSize { small, medium, large }

/// Custom button widget with consistent styling
class AppButton extends StatelessWidget {
  const AppButton({
    required this.text,
    required this.onPressed,
    this.type = AppButtonType.primary,
    this.size = AppButtonSize.medium,
    this.isLoading = false,
    this.isEnabled = true,
    this.icon,
    this.width,
    this.height,
    this.borderRadius,
    this.textStyle,
    super.key,
  });

  final String text;
  final VoidCallback? onPressed;
  final AppButtonType type;
  final AppButtonSize size;
  final bool isLoading;
  final bool isEnabled;
  final IconData? icon;
  final double? width;
  final double? height;
  final BorderRadius? borderRadius;
  final TextStyle? textStyle;

  @override
  Widget build(BuildContext context) {
    final isInteractive = isEnabled && !isLoading && onPressed != null;

    return SizedBox(
      width: width ?? _getWidth(),
      height: height ?? _getHeight(context),
      child: ElevatedButton(
        onPressed: isInteractive ? onPressed : null,
        style: _getButtonStyle(context),
        child: _buildButtonContent(context),
      ),
    );
  }

  Widget _buildButtonContent(BuildContext context) {
    if (isLoading) {
      return SizedBox(
        width: _getIconSize(context),
        height: _getIconSize(context),
        child: CircularProgressIndicator(
          strokeWidth: 2,
          valueColor: AlwaysStoppedAnimation<Color>(_getTextColor()),
        ),
      );
    }

    if (icon != null) {
      return Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(icon, size: _getIconSize(context), color: _getTextColor()),
          SizedBox(width: context.spacingSm),
          Text(text, style: textStyle ?? _getTextStyle(context)),
        ],
      );
    }

    return Text(text, style: textStyle ?? _getTextStyle(context));
  }

  ButtonStyle _getButtonStyle(BuildContext context) {
    return ElevatedButton.styleFrom(
      backgroundColor: _getBackgroundColor(),
      foregroundColor: _getTextColor(),
      disabledBackgroundColor: _getDisabledBackgroundColor(),
      disabledForegroundColor: _getDisabledTextColor(),
      elevation: _getElevation(),
      shadowColor: _getShadowColor(),
      shape: RoundedRectangleBorder(
        borderRadius:
            borderRadius ?? BorderRadius.circular(_getBorderRadius(context)),
        side: _getBorderSide(),
      ),
      padding: _getPadding(context),
    );
  }

  Color _getBackgroundColor() {
    switch (type) {
      case AppButtonType.primary:
        return AppColors.buttonPrimary;
      case AppButtonType.secondary:
        return AppColors.buttonSecondary;
      case AppButtonType.outline:
        return Colors.transparent;
      case AppButtonType.text:
        return Colors.transparent;
    }
  }

  Color _getTextColor() {
    switch (type) {
      case AppButtonType.primary:
        return AppColors.buttonPrimaryText;
      case AppButtonType.secondary:
        return AppColors.buttonSecondaryText;
      case AppButtonType.outline:
        return AppColors.buttonOutlineText;
      case AppButtonType.text:
        return AppColors.onBackground;
    }
  }

  Color _getDisabledBackgroundColor() {
    switch (type) {
      case AppButtonType.primary:
        return AppColors.interactiveDisabled;
      case AppButtonType.secondary:
        return AppColors.interactiveDisabled;
      case AppButtonType.outline:
        return Colors.transparent;
      case AppButtonType.text:
        return Colors.transparent;
    }
  }

  Color _getDisabledTextColor() {
    return AppColors.onBackgroundTertiary;
  }

  double _getElevation() {
    switch (type) {
      case AppButtonType.primary:
        return 2;
      case AppButtonType.secondary:
        return 1;
      case AppButtonType.outline:
        return 0;
      case AppButtonType.text:
        return 0;
    }
  }

  Color? _getShadowColor() {
    switch (type) {
      case AppButtonType.primary:
        return AppColors.cardShadow;
      case AppButtonType.secondary:
        return AppColors.cardShadow;
      case AppButtonType.outline:
        return null;
      case AppButtonType.text:
        return null;
    }
  }

  BorderSide _getBorderSide() {
    switch (type) {
      case AppButtonType.outline:
        return const BorderSide(color: AppColors.buttonOutline, width: 1.5);
      default:
        return BorderSide.none;
    }
  }

  EdgeInsetsGeometry _getPadding(BuildContext context) {
    switch (size) {
      case AppButtonSize.small:
        return EdgeInsets.symmetric(
          horizontal: context.responsiveSpacing(16, scaleFactor: 1.2),
          vertical: context.responsiveSpacing(8, scaleFactor: 1.2),
        );
      case AppButtonSize.medium:
        return EdgeInsets.symmetric(
          horizontal: context.responsiveSpacing(24, scaleFactor: 1.2),
          vertical: context.responsiveSpacing(12, scaleFactor: 1.2),
        );
      case AppButtonSize.large:
        return EdgeInsets.symmetric(
          horizontal: context.responsiveSpacing(32, scaleFactor: 1.2),
          vertical: context.responsiveSpacing(16, scaleFactor: 1.2),
        );
    }
  }

  double _getBorderRadius(BuildContext context) {
    switch (size) {
      case AppButtonSize.small:
        return context.responsiveSpacing(20, scaleFactor: 1.1);
      case AppButtonSize.medium:
        return context.responsiveSpacing(25, scaleFactor: 1.1);
      case AppButtonSize.large:
        return context.responsiveSpacing(30, scaleFactor: 1.1);
    }
  }

  double? _getWidth() {
    switch (size) {
      case AppButtonSize.large:
        return double.infinity;
      default:
        return null;
    }
  }

  double _getHeight(BuildContext context) {
    switch (size) {
      case AppButtonSize.small:
        return context.responsiveSpacing(36, scaleFactor: 1.1);
      case AppButtonSize.medium:
        return context.responsiveSpacing(48, scaleFactor: 1.1);
      case AppButtonSize.large:
        return context.responsiveSpacing(56, scaleFactor: 1.1);
    }
  }

  double _getIconSize(BuildContext context) {
    switch (size) {
      case AppButtonSize.small:
        return context.responsiveSpacing(16, scaleFactor: 1.1);
      case AppButtonSize.medium:
        return context.responsiveSpacing(20, scaleFactor: 1.1);
      case AppButtonSize.large:
        return context.responsiveSpacing(24, scaleFactor: 1.1);
    }
  }

  TextStyle _getTextStyle(BuildContext context) {
    final baseStyle = switch (type) {
      AppButtonType.primary => AppTextStyles.buttonPrimary,
      AppButtonType.secondary => AppTextStyles.buttonSecondary,
      AppButtonType.outline => AppTextStyles.buttonOutline,
      AppButtonType.text => AppTextStyles.buttonSecondary,
    };

    final baseFontSize = switch (size) {
      AppButtonSize.small => 14.0,
      AppButtonSize.medium => 16.0,
      AppButtonSize.large => 18.0,
    };

    final responsiveFontSize = context.responsiveFont(
      baseFontSize,
      scaleFactor: 1.1,
    );

    return baseStyle.copyWith(
      fontSize: responsiveFontSize,
      color: _getTextColor(),
    );
  }
}
