import 'dart:math' as math;
import 'package:flutter/material.dart';
import 'package:safea/core/theme/colors.dart';

/// A reusable widget that creates a black vertical ellipse with wavy colored glow rings
class WavyEllipseEffect extends StatefulWidget {
  const WavyEllipseEffect({
    super.key,
    this.width,
    this.height,
    this.intensity = 1.0,
    this.showLogo = false,
    this.logoText,
    this.logoStyle,
    this.animate = true,
    this.glowColor = AppColors.ellipseGreen,
    this.useDefaultDimensions = false,
    this.sizeFactor = 1.0,
    this.profileImage,
    this.showProfileImage = false,
  });
  final double? width;
  final double? height;
  final double intensity;
  final bool showLogo;
  final String? logoText;
  final TextStyle? logoStyle;
  final bool animate;
  final Color glowColor;
  final bool useDefaultDimensions;
  final double sizeFactor;
  final String? profileImage;
  final bool showProfileImage;

  @override
  State<WavyEllipseEffect> createState() => _WavyEllipseEffectState();
}

class _WavyEllipseEffectState extends State<WavyEllipseEffect>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;

  @override
  void initState() {
    super.initState();
    if (widget.animate) {
      _animationController = AnimationController(
        duration: const Duration(seconds: 3),
        vsync: this,
      )..repeat();
    } else {
      _animationController = AnimationController(
        duration: const Duration(seconds: 1),
        vsync: this,
      );
    }
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    // Calculate dimensions based on Figma specs or provided values
    double effectiveWidth;
    double effectiveHeight;

    if (widget.useDefaultDimensions) {
      // Figma dimensions: 310.39 x 454.32 for 393x852 screen
      final screenWidth = MediaQuery.of(context).size.width;
      final screenHeight = MediaQuery.of(context).size.height;

      // Scale based on current screen vs Figma screen (393x852)
      final scaleX = screenWidth / 393;
      final scaleY = screenHeight / 852;
      final scale = math.min(scaleX, scaleY) * widget.sizeFactor;

      effectiveWidth = 310.39 * scale;
      effectiveHeight = 454.32 * scale;
    } else {
      effectiveWidth =
          (widget.width ?? MediaQuery.of(context).size.width * 0.8) *
          widget.sizeFactor;
      effectiveHeight =
          (widget.height ?? MediaQuery.of(context).size.height * 0.6) *
          widget.sizeFactor;
    }

    return SizedBox(
      width: effectiveWidth,
      height: effectiveHeight,
      child: Stack(
        children: [
          // Wavy colored glow background
          Positioned.fill(
            child: AnimatedBuilder(
              animation: _animationController,
              builder: (context, child) {
                return CustomPaint(
                  painter: WavyEllipsePainter(
                    animationValue: _animationController.value,
                    intensity: widget.intensity,
                    glowColor: widget.glowColor,
                  ),
                );
              },
            ),
          ),
          // Profile image if provided
          if (widget.showProfileImage && widget.profileImage != null)
            Center(
              child: Container(
                width: effectiveWidth * 0.85,
                height: effectiveHeight * 0.80,
                child: ClipOval(
                  child: widget.profileImage!.startsWith('http')
                      ? Image.network(
                          widget.profileImage!,
                          fit: BoxFit.cover,
                          errorBuilder: (context, error, stackTrace) {
                            // Fallback if image doesn't exist
                            return Container(
                              color: Colors.grey[300],
                              child: Icon(
                                Icons.person,
                                size: effectiveWidth * 0.3,
                                color: Colors.grey,
                              ),
                            );
                          },
                        )
                      : Image.asset(
                          widget.profileImage!,
                          fit: BoxFit.cover,
                          errorBuilder: (context, error, stackTrace) {
                            // Fallback if image doesn't exist
                            return Container(
                              color: Colors.grey[300],
                              child: Icon(
                                Icons.person,
                                size: effectiveWidth * 0.3,
                                color: Colors.grey,
                              ),
                            );
                          },
                        ),
                ),
              ),
            ),
          // Logo if provided
          if (widget.showLogo && widget.logoText != null)
            Center(
              child: Text(
                widget.logoText!,
                style:
                    widget.logoStyle ??
                    const TextStyle(
                      fontFamily: 'Sana Sans VAR',
                      fontSize: 17,
                      fontWeight: FontWeight.w400,
                      color: Colors.white,
                      letterSpacing: 2,
                    ),
              ),
            ),
        ],
      ),
    );
  }
}

/// Custom painter that creates the wavy colored glow effect around a black vertical ellipse
class WavyEllipsePainter extends CustomPainter {
  WavyEllipsePainter({
    required this.animationValue,
    this.intensity = 1.0,
    this.glowColor = const Color(0xFF00FF88),
  });
  final double animationValue;
  final double intensity;
  final Color glowColor;

  @override
  void paint(Canvas canvas, Size size) {
    final center = Offset(size.width / 2, size.height / 2);

    // Create the black vertical ellipse in the center
    final ellipseRect = Rect.fromCenter(
      center: center,
      width: size.width * 0.95,
      height: size.height * 0.90,
    );

    // First draw the wavy green glow rings
    _drawWavyGlowRings(canvas, size, center, ellipseRect);

    // Then draw the black ellipse on top
    final blackPaint = Paint()
      ..color = Colors.black
      ..style = PaintingStyle.fill;

    canvas.drawOval(ellipseRect, blackPaint);
  }

  void _drawWavyGlowRings(
    Canvas canvas,
    Size size,
    Offset center,
    Rect ellipseRect,
  ) {
    // Create multiple rings with different intensities and wave patterns
    final rings = [
      (radius: 0.5, opacity: 0.3, waves: 3, amplitude: 8.0),
      (radius: -0.8, opacity: 0.3, waves: 2, amplitude: -8.0),
      (radius: 0.8, opacity: 0.5, waves: 1, amplitude: 12.0),
      (radius: 1.0, opacity: 0.8, waves: 1, amplitude: 12.0),
      (radius: 0.7, opacity: 0.7, waves: 2, amplitude: -12.0),
      (radius: 0.6, opacity: 0.4, waves: 2, amplitude: 14.0),
      (radius: -0.5, opacity: 0.3, waves: 3, amplitude: -8.0),
    ];

    for (final ring in rings) {
      _drawWavyRing(
        canvas,
        center,
        ellipseRect,
        ring.radius,
        ring.opacity * intensity,
        ring.waves,
        ring.amplitude * intensity,
      );
    }
  }

  void _drawWavyRing(
    Canvas canvas,
    Offset center,
    Rect ellipseRect,
    double radiusMultiplier,
    double opacity,
    int waveCount,
    double amplitude,
  ) {
    final path = Path();
    final dynamicOpacity =
        opacity * (0.8 + 0.4 * math.sin(animationValue * 2 * math.pi));
    final paint = Paint()
      ..color = Color.fromARGB(
        (255 * dynamicOpacity).round().clamp(0, 255),
        (glowColor.r * 255.0).round() & 0xff,
        (glowColor.g * 255.0).round() & 0xff,
        (glowColor.b * 255.0).round() & 0xff,
      )
      ..style = PaintingStyle.stroke
      ..strokeWidth = 25 + amplitude
      ..maskFilter = MaskFilter.blur(BlurStyle.normal, 8.0 + amplitude / 4);

    // Calculate ellipse parameters
    final baseRadiusX = ellipseRect.width / 1.6 /* * radiusMultiplier */;
    final baseRadiusY = ellipseRect.height / -1.9 /* * radiusMultiplier */;

    var firstPoint = true;

    // Create wavy elliptical path
    for (var i = 0; i <= 360; i += 3) {
      final angle = i * math.pi / 180;

      // Calculate wave offset with time-based animation
      final waveOffset =
          math.sin(waveCount * angle + animationValue * 4 * math.pi) *
          amplitude;

      // Calculate elliptical coordinates with wave
      final radiusX = baseRadiusX + waveOffset;
      final radiusY =
          baseRadiusY + waveOffset * 0.6; // Less wave on Y axis for ellipse

      final x = center.dx + radiusX * math.cos(angle);
      final y = center.dy + radiusY * math.sin(angle);

      if (firstPoint) {
        path.moveTo(x, y);
        firstPoint = false;
      } else {
        path.lineTo(x, y);
      }
    }

    path.close();
    canvas.drawPath(path, paint);
  }

  @override
  bool shouldRepaint(covariant WavyEllipsePainter oldDelegate) {
    return oldDelegate.animationValue != animationValue ||
        oldDelegate.intensity != intensity;
  }
}
