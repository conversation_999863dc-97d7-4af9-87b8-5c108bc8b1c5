import 'package:flutter/material.dart';
import 'package:safea/core/theme/app_text_styles.dart';

/// A reusable text widget that provides consistent styling throughout the app
///
/// This widget should be used for ALL text in the app to ensure consistency.
/// It supports predefined styles from AppTextStyles and custom styling.
///
/// Example usage:
/// ```dart
/// TextWidget(
///   'Ein Klick. Sofort helfen. Sicherheit bieten'.hardcoded,
///   style: AppTextStyles.tagline,
/// )
///
/// TextWidget(
///   'Welcome to Safea'.hardcoded,
///   style: AppTextStyles.responsiveTagline(context),
/// )
/// ```
class TextWidget extends StatelessWidget {
  /// Creates a TextWidget with consistent styling
  const TextWidget(
    this.text, {
    super.key,
    this.style,
    this.textAlign = TextAlign.start,
    this.maxLines,
    this.overflow = TextOverflow.ellipsis,
    this.softWrap = true,
    this.textScaleFactor = 1.0,
    this.semanticsLabel,
  });

  /// The text content to display
  final String text;

  /// Text style - should use AppTextStyles for consistency
  final TextStyle? style;

  /// Text alignment
  final TextAlign textAlign;

  /// Maximum number of lines
  final int? maxLines;

  /// Text overflow behavior
  final TextOverflow overflow;

  /// Whether text should break at soft line breaks
  final bool softWrap;

  /// Text scale factor (default 1.0 to prevent system scaling)
  final double textScaleFactor;

  /// Semantic label for accessibility
  final String? semanticsLabel;

  @override
  Widget build(BuildContext context) {
    return Text(
      text,
      style: style ?? AppTextStyles.bodyMedium,
      textAlign: textAlign,
      maxLines: maxLines,
      overflow: overflow,
      softWrap: softWrap,
      semanticsLabel: semanticsLabel,
    );
  }
}

/// Extension to provide quick access to common text styles
extension TextWidgetExtensions on TextWidget {
  /// Creates a heading text widget
  static TextWidget heading(
    String text, {
    Key? key,
    TextStyle? style,
    TextAlign textAlign = TextAlign.start,
    int? maxLines,
  }) {
    return TextWidget(
      text,
      key: key,
      style: style ?? AppTextStyles.headlineLarge,
      textAlign: textAlign,
      maxLines: maxLines,
    );
  }

  /// Creates a body text widget
  static TextWidget body(
    String text, {
    Key? key,
    TextStyle? style,
    TextAlign textAlign = TextAlign.start,
    int? maxLines,
  }) {
    return TextWidget(
      text,
      key: key,
      style: style ?? AppTextStyles.bodyMedium,
      textAlign: textAlign,
      maxLines: maxLines,
    );
  }

  /// Creates a caption text widget
  static TextWidget caption(
    String text, {
    Key? key,
    TextStyle? style,
    TextAlign textAlign = TextAlign.start,
    int? maxLines,
  }) {
    return TextWidget(
      text,
      key: key,
      style: style ?? AppTextStyles.caption,
      textAlign: textAlign,
      maxLines: maxLines,
    );
  }
}
