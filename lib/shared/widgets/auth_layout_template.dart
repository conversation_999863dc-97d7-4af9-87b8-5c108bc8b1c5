import 'package:flutter/material.dart';
import 'package:safea/core/theme/app_text_styles.dart';
import 'package:safea/core/theme/responsive_extensions.dart';
import 'package:safea/shared/extensions/string_hardcoded.dart';
import 'package:safea/shared/widgets/text_widget.dart';

/// Standardized layout template for authentication and setup screens
///
/// This template provides a consistent layout structure with:
/// 1. Title (positioned at top-left)
/// 2. Content (centered, varies by screen)
/// 3. Description (bottom section)
/// 4. Actions row (below description, contains button(s) and optional clickable text)
class AuthLayoutTemplate extends StatelessWidget {
  const AuthLayoutTemplate({
    super.key,
    this.title,
    this.content,
    this.description,
    this.primaryButton,
    this.secondaryText,
    this.onSecondaryTextTap,
    this.backgroundColor,
    this.showDefaultTitle = true,
  });

  /// Title widget (optional, uses default if not provided and showDefaultTitle is true)
  final Widget? title;

  /// Main content widget (centered)
  final Widget? content;

  /// Description text (bottom section)
  final String? description;

  /// Primary button widget
  final Widget? primaryButton;

  /// Secondary clickable text (right-aligned in actions row)
  final String? secondaryText;

  /// Callback for secondary text tap
  final VoidCallback? onSecondaryTextTap;

  /// Background color (optional)
  final Color? backgroundColor;

  /// Whether to show the default title
  final bool showDefaultTitle;

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: backgroundColor != null
          ? BoxDecoration(color: backgroundColor)
          : const BoxDecoration(
              gradient: RadialGradient(
                radius: 1.2,
                colors: [
                  Color(0xFF00FF88), // Bright green center
                  Color(0xFF00CC66), // Medium green
                  Color(0xFF009944), // Darker green
                  Color(0xFF001122), // Very dark background
                ],
                stops: [0.0, 0.3, 0.6, 1.0],
              ),
            ),
      child: SafeArea(
        child: Padding(
          padding: context.screenPaddingHorizontal,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Title section (top-left)
              if (showDefaultTitle || title != null) ...[
                SizedBox(height: context.spacingXl),
                _buildTitle(context),
                SizedBox(height: context.spacingXl),
              ],

              // Content section (centered, expandable)
              Expanded(child: content ?? const SizedBox.shrink()),

              // Description section (bottom)
              if (description != null) ...[
                _buildDescription(context),
                SizedBox(height: context.spacingLg),
              ],

              // Actions section (bottom)
              if (primaryButton != null || secondaryText != null) ...[
                _buildActions(context),
                SizedBox(height: context.spacingXl),
              ],
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildTitle(BuildContext context) {
    if (title != null) {
      return title!;
    }

    if (showDefaultTitle) {
      return TextWidget(
        'Ein Klick.\nSofort helfen.\nSicherheit bieten.'.hardcoded,
        style: AppTextStyles.authTitle(context),
      );
    }

    return const SizedBox.shrink();
  }

  Widget _buildDescription(BuildContext context) {
    return TextWidget(
      description!,
      style: AppTextStyles.authDescription(context),
    );
  }

  Widget _buildActions(BuildContext context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        // Primary button (left-aligned)
        if (primaryButton != null) Expanded(flex: 3, child: primaryButton!),

        // Spacer if both button and text are present
        if (primaryButton != null && secondaryText != null)
          SizedBox(width: context.spacingMd),

        // Secondary clickable text (right-aligned)
        if (secondaryText != null)
          Expanded(
            child: GestureDetector(
              onTap: onSecondaryTextTap,
              child: TextWidget(
                secondaryText!,
                style: AppTextStyles.authClickableText(context),
                textAlign: TextAlign.end,
              ),
            ),
          ),
      ],
    );
  }
}

/// Standardized button for auth/setup screens
class AuthButton extends StatelessWidget {
  const AuthButton({
    required this.text,
    required this.onPressed,
    this.isLoading = false,
    super.key,
  });

  final String text;
  final VoidCallback? onPressed;
  final bool isLoading;

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      height: context.responsiveSpacing(48), // Responsive button height
      child: ElevatedButton(
        onPressed: isLoading ? null : onPressed,
        style: ElevatedButton.styleFrom(
          backgroundColor: const Color(0xFFFFFFFF), // White background
          foregroundColor: const Color(0xFF1F1F1F), // Dark text
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(24), // 24px border radius
          ),
          padding: EdgeInsets.symmetric(
            horizontal: context.responsiveSpacing(12),
            vertical: context.responsiveSpacing(6),
          ),
          elevation: 0,
        ),
        child: isLoading
            ? const SizedBox(
                width: 20,
                height: 20,
                child: CircularProgressIndicator(
                  strokeWidth: 2,
                  valueColor: AlwaysStoppedAnimation<Color>(Color(0xFF1F1F1F)),
                ),
              )
            : TextWidget(
                text,
                style: AppTextStyles.authButtonText(context),
                textAlign: TextAlign.center,
              ),
      ),
    );
  }
}

/// Extension for easy access to auth layout patterns
extension AuthLayoutExtensions on Widget {
  /// Wrap widget in auth layout template
  Widget wrapInAuthLayout({
    String? title,
    String? description,
    Widget? primaryButton,
    String? secondaryText,
    VoidCallback? onSecondaryTextTap,
    Color? backgroundColor,
    bool showDefaultTitle = true,
  }) {
    return AuthLayoutTemplate(
      title: title != null ? Text(title) : null,
      content: this,
      description: description,
      primaryButton: primaryButton,
      secondaryText: secondaryText,
      onSecondaryTextTap: onSecondaryTextTap,
      backgroundColor: backgroundColor,
      showDefaultTitle: showDefaultTitle,
    );
  }
}
